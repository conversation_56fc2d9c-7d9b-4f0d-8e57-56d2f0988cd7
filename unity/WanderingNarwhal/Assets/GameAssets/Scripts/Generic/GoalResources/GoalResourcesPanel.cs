using System;
using System.Collections;
using System.Collections.Generic;
using BBB.DI;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.GoalsService;
using Bebopbee.Core.Extensions.Unity;
using BebopBee.Core;
using BebopBee.Core.Audio;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;
using GameAssets.Scripts.Messages;
using GameAssets.Scripts.Utils;
using UnityEngine;

namespace BBB.UI.Level
{
    public interface IGoalUiElement
    {
        bool IsCompleted { get; }
        void Highlight(IEnumerable<GameObject> highlightPrefabs);
        void StopHighlight();
    }

    public class GoalResourcesPanel : BbbMonoBehaviour, IContextInitializable, IContextReleasable, IBoardRevealObserver,
        ILevelRevealObserver
    {
        [Header("Goal reveal config")][SerializeField]
        private float _goalRevealDelay = 0.3f;

        [SerializeField] private float _goalRevealTime = 0.6f;
        [SerializeField] private Ease _goalRevealEase = Ease.OutBack;

        [Header("References")][SerializeField]
        private GameObject _goalItemPrefab;

        [SerializeField] private Transform _listGoals;
        [SerializeField] private Transform _listGoals2;
        [SerializeField] private List<GameObject> _highlightPrefabs;
        [SerializeField] private SharedGoalVisualSettingsScriptableObject _sharedGoalVisualSettings;
        [field:SerializeField] public RectTransform InverseMaskTarget { get; private set; }

        private IGridController _gridController;
        private TileController _tileController;
        private RendererContainers _rendererContainers;
        private TileResourceSelector _tileResourceSelector;
        private TilesResources _tilesResources;
        private FxRenderer _fxRenderer;
        private M3Settings _settings;

        private readonly List<GoalResourceItem> _allItems = new();
        private readonly List<IGoalUiElement> _allGoalElements = new();

        private IEventDispatcher _eventDispatcher;
        private GoalViewHelper _goalViewHelper;
        private bool _allGoalsFinished;
        private float _itemScale = 1f;

        [SerializeField] private GoalResourcesPanelGameEventHandler _gameEventGoalPanel;

        private static readonly Action<string> CachedPlaySoundAction = AudioProxy.PlaySound;
        private readonly Dictionary<GoalType, GoalResourceItem> _goalsItems = new();

        private readonly List<Coords> _coordsList = new();
        private Coords _lastSpawnPos;

        private IEnumerable<GoalResourceItem> AllItems => _allItems;

        private readonly Dictionary<GoalType, GoalType> _linkedGoalTypes = new()
        {
            {GoalType.Backgrounds, GoalType.Bush},
            {GoalType.Petal, GoalType.FlowerPot},
        };

        public void InitializeByContext(IContext context)
        {
            if (_eventDispatcher == null)
            {
                // Initialize global services only once (If screen is cached).
                _eventDispatcher = context.Resolve<IEventDispatcher>();
                _tileResourceSelector = context.Resolve<TileResourceSelector>();
                _tilesResources = context.Resolve<TilesResources>();
                _fxRenderer = context.Resolve<FxRenderer>();
            }

            // These observes should be added every time,
            // since they are cleaned-up in the level and board revealers
            var levelRevealer = context.Resolve<ILevelRevealer>();
            levelRevealer.AddObserver(this);
            _gridController = context.Resolve<IGridController>();
            _gridController.AddRevealObserver(this);

            _tileController = context.Resolve<TileController>();
            _rendererContainers = context.Resolve<RendererContainers>();
            _goalViewHelper = context.Resolve<GoalViewHelper>();
            _settings = context.Resolve<M3Settings>();

            if (_gameEventGoalPanel != null)
            {
                _gameEventGoalPanel.Init(context);
            }
        }

        private void HandleGoalFilled(IGoalUiElement justFilledElement)
        {
            IGoalUiElement remainingElement = null;
            int remainingCount = 0;

            foreach (var element in _allGoalElements)
                if (!element.IsCompleted)
                {
                    remainingElement = element;
                    remainingCount++;
                }

            if (remainingCount == 1)
            {
                remainingElement?.Highlight(_highlightPrefabs);
            }
            else
            {
                foreach (var element in _allGoalElements)
                    element.StopHighlight();
            }
        }

        public void VisualizeGoalProgressIfNeeded(GoalType goal, DamageSource damageSource, Coords coords,
            int tileId = 0, int? skin = null,
            Coords coordsOffset = default(Coords), int matchIndex = -1, int sameTileCount = -1)
        {
            VisualizeGoalProgressIfNeeded(new GoalTypeTagPair(goal), damageSource, coords, tileId, skin, coordsOffset,
                matchIndex, sameTileCount);
        }

        public void VisualizeGoalProgressIfNeeded(GoalTypeTagPair goalTypeTagPair, DamageSource damageSource, Coords coords, int tileId = 0, int? skin = null,
            Coords coordsOffset = default(Coords), int matchIndex = -1, int sameTileCount = -1)
        {
            if (goalTypeTagPair.GoalType == GoalType.GameEventScore)
                HandleGameEventScoreGoal(coords, sameTileCount, goalTypeTagPair.GoalTag);

            var goal = goalTypeTagPair.GoalType;

            if (!_goalsItems.ContainsKey(goal))
                return;
            
            var goalItem = _goalsItems[goal];
            
            // BDebug.LogDebugError($"VisualizeGoalProgressIfNeeded {tileId} at coords {coords} at time {Time.time} and frame {Time.frameCount}");
            
            IncreaseItemCount(goalItem, coords, tileId, skin: skin, coordsOffset: 
                coordsOffset, matchIndex, damageSource, goal);
        }
        
        private void HandleGameEventScoreGoal(Coords coords, int sameTileCount, string goalTag)
        {
            _coordsList.Add(coords);

            if(_coordsList.Count < sameTileCount || _gameEventGoalPanel == null) return;

            var spawnPos = _coordsList[_coordsList.Count / 2];

            float delay = 0;
            if (spawnPos == _lastSpawnPos)
            {
                delay = _settings.CollectEventTileDelay;
            }
            
            Rx.Invoke(delay, _ =>
                {
                    _gameEventGoalPanel.OnGameEventScoreCollected(goalTag, spawnPos, Mathf.Max(1, sameTileCount));
                });
            _coordsList.Clear();
            _lastSpawnPos = spawnPos;
        }

        public void OnTileCreated(Tile tile)
        {
            foreach (var goalItem in _goalsItems)
            {
                var goal = goalItem.Key;
                if (goal.CanReplenish())
                {
                    if (GoalState.IsTileGridBasedGoalRelatedItem(tile, goal))
                    {
                        if (goal != GoalType.Sand)
                        {
                            // Only sand tile can pass all checks.
                            Debug.LogError("Non-sand goal increase max count from created tile. Goal=" + goal);
                        }

                        IncrementCounter(goalItem.Value, 1);
                    }
                }
            }
        }

        public void OnTileModCreated(TileState state)
        {
            foreach (var goalItem in _goalsItems)
            {
                var goal = goalItem.Key;
                if (goal.CanReplenish())
                {
                    if (GoalState.IsModStateRelatedToGoal(state, goal))
                    {
                        if (goal != GoalType.Sand)
                        {
                            // Only sand mod can pass all checks.
                            Debug.LogError("Non-sand goal increase max count from created tile mod. Goal=" + goal);
                        }

                        if (!goalItem.Value.IsCompleted)
                            IncrementCounter(goalItem.Value, 1);
                    }
                }
            }
        }

        public void OnTileModRemoved(Coords coords, TileState state, DamageSource damageSource)
        {
            foreach (var goalItem in _goalsItems)
            {
                var goal = goalItem.Key;
                if (GoalState.IsModStateRelatedToGoal(state, goal))
                {
                    IncreaseItemCount(goalItem.Value, coords, damageSource:damageSource, goalType:goal);
                }
            }
        }

        public void ReleaseByContext(IContext context)
        {
        }

        public async UniTask FillGoalsAsync(ILevel level)
        {
            Clear();

            var originalGoals = level.Goals;

            var goalViewData = await _goalViewHelper.GetGoalsViewDataAsync();

            for (var i = 0; i < goalViewData.Count; ++i)
            {
                var goalViewDatum = goalViewData[i];
                var item = CreateGo(i);
                item.Init(goalViewDatum);
                _goalsItems[goalViewDatum.Type] = item;
                _allItems.Add(item);
                _allGoalElements.Add(item);
            }
            
            _listGoals.gameObject.SetActive(goalViewData.Count > 0);
            _listGoals2.gameObject.SetActive(goalViewData.Count > 2);
        }
        
        public async UniTask AddGoals(Grid grid, GoalsSystem goalsSystem, GoalType goalType)
        {
            var goalViewData = await _goalViewHelper.GetGoalsViewDataAsync();

            foreach (var goalViewDatum in goalViewData)
            {
                goalViewDatum.TotalCount = goalsSystem.GetExistingGoalCount(grid, goalType);
                
                if (goalViewDatum.Type == goalType)
                {
                    if (!_goalsItems.ContainsKey(goalViewDatum.Type))
                    {
                        var item = CreateGo( _listGoals.childCount > 1 ? 2 : 1);
                        item.Init(goalViewDatum);
                        
                        _goalsItems[goalViewDatum.Type] = item;
                        item.transform.GetChild(0).localScale = Vector3.zero;
                        
                        if (!_allItems.Contains(item))
                        {
                            _allItems.Add(item);
                        }

                        if (!_allGoalElements.Contains(item))
                        {
                            _allGoalElements.Add(item);
                        }
                        
                        var tf = item.transform.GetChild(0);
                        tf.DOScale(_itemScale, _goalRevealTime).SetEase(_goalRevealEase).OnComplete(() => item.PlayParticleAnimation());
                    }
                    else
                    {
                        _goalsItems[goalType].Init(goalViewDatum);
                    }
                }
            }
            _listGoals.gameObject.SetActive(goalViewData.Count > 0);
            _listGoals2.gameObject.SetActive(goalViewData.Count > 2);
        }

        private void IncreaseItemCount(GoalResourceItem item, Coords coords, int tileId = 0, int? skin = null,
            Coords coordsOffset = default(Coords), int matchIndex = -1, DamageSource damageSource = DamageSource.None, GoalType goalType = GoalType.None)
        {
            if (item.IsCompleted)
                return;

            var localPos = _gridController.ToDisplacedLocalPosition(coords);
            var from = (Vector2)_gridController.Transform.localToWorldMatrix.MultiplyPoint(localPos);

            if (item.GoalType.IsGoalTileHaveSkins())
            {
                var tileView = _tileController.GetDestroyedTileViewById(tileId);
                if (tileView == null)
                {
                    // Some special tiles may produce Goal while still alive on grid.
                    // For example, SquidTile. -VK
                    tileView = _tileController.GetExistingTileView(tileId);
                }

                if (!skin.HasValue)
                {
                    int skinNum = 0;
                    if (tileView != null)
                    {
                        if (tileView.TryGetTileViewSkinIndex(ref skinNum, coords))
                        {
                            skin = skinNum;
                        }

                        Vector2 offsetCoords = new Vector2();
                        if (tileView.TryGetFxSpawnOffset(ref offsetCoords))
                        {
                            var offset = _gridController.ToLocalPosition(offsetCoords - new Vector2(0.5f, 0.5f));
                            from += offset;
                        }
                    }
                }
                else
                {
                    if (coordsOffset.X != 0 || coordsOffset.Y != 0)
                    {
                        var offset = _gridController.ToLocalPosition(new Vector2(coordsOffset.X - 0.5f, coordsOffset.Y - 0.5f));
                        from += offset;
                    }
                }
            }

            if (_sharedGoalVisualSettings.IsGoalFlyingAllowed(item.GoalType))
            {
                var goalFlySettings = GetGoalAnimationFromFx(item, @from, skin);
                
                var rocketWithTileKindGoal = goalType.IsTileKind() && (damageSource & (DamageSource.LineBreakerArrow | DamageSource.TukTuk)) != 0;

                if (rocketWithTileKindGoal)
                {
                    goalFlySettings.rocketRelatedDelay = _settings.PauseAfterRocketHit;
                }
                
                LaunchIconFlyAnimation(goalFlySettings, matchIndex);
            }
            else
            {
                AchieveAtomicGoalStep(item);
            }
        }

        private void LaunchIconFlyAnimation(GoalIconFlySettings flySettings, int matchIndex)
        {
            switch (flySettings.flyMode)
            {
                case GoalFlyMode.DoFlyAnimator:
                {
                    DoFlyAnimator.Instance.Launch(flySettings,  playSoundMethod: CachedPlaySoundAction);
                    break;
                }
                case GoalFlyMode.GoalFlyAnimator:
                {
                    GoalIconFlyAnimator.Instance.Launch(flySettings);
                    break;
                }
                case GoalFlyMode.WaypointAnimator:
                {
                    WaypointFlyAnimator.Instance.Launch(flySettings, matchIndex, _gridController.GetCenterPosition(), _tilesResources.CellSize);
                    break;
                }
            }
        }

        private void IncrementCounter(GoalResourceItem item, int count)
        {
            if (item.GoalType is GoalType.Sand or GoalType.Backgrounds or GoalType.Petal)
            {
                item.ChangeCounter(count);
            }
        }

        private void AchieveAtomicGoalStep(GoalResourceItem item)
        {
            item.PlayHit();

            var enableCheckMarkOnZero = false;

            if (_linkedGoalTypes.TryGetValue(item.GoalType, out var value))
            {
                if (_goalsItems.ContainsKey(value) && !_goalsItems[value].IsCompleted)
                {
                    enableCheckMarkOnZero = true;
                }
            }

            item.DecreaseCounter(enableCheckMarkOnZero);
                
            if (item.IsCompleted)
            {
                HandleGoalFilled(item);
                if (item.IsCompleted && !enableCheckMarkOnZero)
                {
                    var goalUiItemFilled = _eventDispatcher.GetMessage<GoalUiItemFilled>();
                    goalUiItemFilled.Set(item.GoalType);
                    _eventDispatcher.TriggerEventNextFrame(goalUiItemFilled);
                }
            }
            else
            {
                if (item.GoalType == GoalType.DropItems)
                {
                    // Drop Item goal should trigger HelpingHand reveal every time each drop item is collected,
                    // unlike all other types of goals, when they trigger helping hands reveal only after full complete. -VK
                    var goalUiItemFilled = _eventDispatcher.GetMessage<GoalUiItemFilled>();
                    goalUiItemFilled.Set(item.GoalType);
                    _eventDispatcher.TriggerEventNextFrame(goalUiItemFilled);
                }
            }
        }

        /// <summary>
        /// Get goal fly animation asset and convert it to settings data
        /// and return this as result. The data then will be used for goal icon fly animation. 
        /// </summary>
        /// <param name="item">Goal UI item.</param>
        /// <param name="from">Start position world vector.</param>
        /// <param name="skin">Skin index extracted from tile on grid (if tile can have skin).</param>
        /// <remarks>
        /// All goals animations consist of GoalIconFly scriptable object asset and FxType which will be spawned to fly in this animation.
        /// These settings are stored inside MatchResourcesExtra asset, which contains mapping of GoalType, FxType and GoalIconFlySettings. -VK
        /// </remarks>
        private GoalIconFlySettings GetGoalAnimationFromFx(GoalResourceItem item, Vector2 from, int? skin)
        {
            _tileResourceSelector.GetGoalFlySettinsForGoal(item.GoalType, out var fx, out var flySettingsAsset);

            var to = item.GetIcon().GetComponent<RectTransform>().GetWorldCenterPos();
            

            void OnTargetReached()
            {
                AchieveAtomicGoalStep(item);
            }

            var result = flySettingsAsset.ToSettingsData(from, to, fx, _gridController,
                _tileResourceSelector, _fxRenderer, _rendererContainers, item.GetSprite(),
                skin, _tilesResources, onTargetReached: OnTargetReached, onEnd: null);

            return result;
        }

        public void SetAllGoalsFinished()
        {
            if (_allGoalsFinished)
                return;

            foreach (var item in _allItems)
            {
                var delta = item.SetValueToZero();
                for (int i = 0; i < delta; i++)
                {
                    var goalUiItemFilled = _eventDispatcher.GetMessage<GoalUiItemFilled>();
                    goalUiItemFilled.Set(item.GoalType);
                    _eventDispatcher.TriggerEventNextFrame(goalUiItemFilled);
                }

                if (delta > 0)
                    HandleGoalFilled(item);
            }

            _allGoalsFinished = true;
        }

        public void Clear()
        {
            _allGoalsFinished = false;
            if (_allItems != null)
            {
                foreach (var item in _allItems)
                {
                    if (item != null) Destroy(item.gameObject);
                }

                _allItems.Clear();
            }
            _goalsItems.Clear();
            _allGoalElements.Clear();
        }

        private GoalResourceItem CreateGo(int index)
        {
            UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{_goalItemPrefab.name}]");
            var parentRow = index < 2 ? _listGoals : _listGoals2;
            var go = Instantiate(_goalItemPrefab, parentRow, false);
            UnityEngine.Profiling.Profiler.EndSample();
            var item = go.GetComponent<GoalResourceItem>();
            go.SetActive(true);
            return item;
        }

        public void OnBoardRevealStart()
        {
            StartCoroutine(RevealGoalItemsRoutine());
        }

        private IEnumerator RevealGoalItemsRoutine()
        {
            foreach (var item in AllItems)
            {
                yield return WaitCache.Seconds(_goalRevealDelay);
                // child 0 is a container for scaling content from the center
                var tf = item.transform.GetChild(0);
                tf.DOScale(_itemScale, _goalRevealTime).SetEase(_goalRevealEase).OnComplete(() => item.PlayParticleAnimation());
            }
        }

        public void OnLevelRevealStart()
        {
            foreach (var item in AllItems)
                item.transform.GetChild(0).localScale = Vector3.zero;
        }
    }

    public class GoalUiItemFilled : Message<GoalType>
    {
    }
}
