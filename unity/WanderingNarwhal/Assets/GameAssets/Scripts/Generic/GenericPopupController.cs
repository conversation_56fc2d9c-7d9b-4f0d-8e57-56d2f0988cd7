using UnityEngine;
using DG.Tweening;

namespace BBB {
	public class GenericPopupController : BbbMonoBehaviour {
		[SerializeField] LocalizedText titleText;
		[SerializeField] Transform headerIconT;
		[SerializeField] Transform window;
		[SerializeField] Transform innerWindow;

        IPopupButtonController buttonsController;

        public void SetButtonsController(IPopupButtonController controller)
        {
            buttonsController = controller;
        }

		GenericPopupContentController _contentHolder;
        PopupFactory.OnButtonPressed onButtonPressed;
        int code;

		public GenericPopupContentController content {
			get { return _contentHolder; }
		}

		void Start()
		{
			window.DOScale(Vector3.zero, 0.5f)
				.SetEase(Ease.OutBack)
				.From();
		}

        public void WindowButtonPressed()
        {
            if (onButtonPressed != null)
                onButtonPressed(code);
        }

        public void setupWindowButton(BBB.PopupFactory.OnButtonPressed onButtonPressed, int code)
        {
            this.onButtonPressed = onButtonPressed;
            this.code = code;
        }

        public void setupWindowButton(int code)
        {
            this.code = code;
        }
        
		public void setTitle(string title, params object[] args)
		{
			titleText.SetTextId(title, args);
		}

        public void setHeaderFont(Font font)
        {
            titleText.font = font;
        }

        public void setButtonsFont(Font font)
        {
            buttonsController.setFont(font);
        }

		public void setHeaderIcon(string iconPrefabName)
		{
            if( !string.IsNullOrEmpty(iconPrefabName) )
            {
                var res = Resources.Load(iconPrefabName);

                if( res != null )
                {
                    headerIconT.gameObject.SetActive(true);
                    UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[{res.name}]");
                    GameObject icon = GameObject.Instantiate(res) as GameObject;
                    UnityEngine.Profiling.Profiler.EndSample();
                    icon.transform.SetParent(headerIconT, false);
                }
            }
		}

		public void setContentHolder(GenericPopupContentController contentHolder)
		{
			contentHolder.transform.SetParent(innerWindow, false);
			_contentHolder = contentHolder;
		}
	}
}
