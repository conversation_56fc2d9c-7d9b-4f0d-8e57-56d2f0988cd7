using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Events;

namespace BBB {

    public interface IPopupButtonController
    {
        void setFont(Font font);
    }

	public class GenericPopupSingleButtonController : Bbb<PERSON>onoBehaviour, IPopupButtonController
    {
        private Button button;
        private LocalizedText text;

		public static GenericPopupSingleButtonController Create(Transform parent, string buttonText, UnityAction onButtonPressed)
		{
            UnityEngine.Profiling.Profiler.BeginSample($"Instantiate[GenericPopup_SingleButton_P]");
			GameObject buttonObject = Instantiate(
				Resources.Load("GenericPopup_SingleButton_P"),
				parent,
				false
			) as GameObject;
            UnityEngine.Profiling.Profiler.EndSample();
			var controller = buttonObject.GetComponent<GenericPopupSingleButtonController>();
            controller.button = controller.GetComponent<Button>();

            controller.button.onClick.AddListener(() => {
				if( onButtonPressed != null )
					onButtonPressed();
			});

            controller.text = controller.button.transform.Find("Text").GetComponent<LocalizedText>();
            controller.text.SetTextId(buttonText);

            return controller;
		}

        public void setFont(Font font)
        {
            text.font = font;
        }

#if UNITY_ANDROID
        void OnGUI()
        {
            GUI.depth = -1;
            var e = Event.current;

            if (e.isKey && e.type == EventType.KeyDown && e.keyCode == KeyCode.Escape)
            {
                button.onClick.Invoke();
                e.Use();
            }
        }
#endif
    }
}