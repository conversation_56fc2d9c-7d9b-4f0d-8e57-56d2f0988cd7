#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using BBB.Core.ResourcesManager;
using BBB.Match3.Renderer;
using BBB.UI.Level;
using BebopBee.Core.Audio;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;
using UnityEngine.Audio;

namespace BBB.UI
{
    public static class EditorPrefabNames
    {
        public const string M3EditorDebugView = "M3EditorDebugView";
        public const string DebugViewSpawner = "DebugViewSpawner";
        public const string DebugViewDespawner = "DebugViewDespawner";
        public const string DebugViewCoord = "DebugViewCoord";
        public const string DebugViewNotShuffable = "DebugViewNotShuffable";
        public const string DebugViewInvisibleWalls = "DebugViewInvisibleWalls";
    }

    public class Match3EditorResourceProvider : IMatch3SharedResourceProvider
    {
        public IAssetsManager AssetsManager { get; }

        public IEnumerable<AudioMixer> Mixers => _mixersDict.Values;

        private readonly Dictionary<string, AudioMixer> _mixersDict;
        private readonly Dictionary<string, Sprite> _sprites = new();
        private readonly Dictionary<string, GameObject> _prefabs = new();

        private readonly Dictionary<string, GameObject> _editorPrefabsDict;
        private readonly Dictionary<string, AudioContextSettings> _editorAudioContextDict;

        public void AddEditorPrefab(GameObject gameObject)
        {
            _editorPrefabsDict.Add(gameObject.name, gameObject);
        }

        public GameObject GetEditorPrefab(string name)
        {
            return _editorPrefabsDict.GetValueOrDefault(name);
        }

        public Sprite GetSprite(string name)
        {
            return _sprites.GetValueOrDefault(name);
        }

        public AudioContextSettings GetAudioContext(string name)
        {
            return _editorAudioContextDict.GetValueOrDefault(name);
        }

        public async UniTask<T> CacheAndLoadAsync<T>(
            object            caller,
            string            name,
            AssetLoadPriority priority = AssetLoadPriority.InQueue
        ) where T : UnityEngine.Object
        {
            var assetLoaded = await AssetsManager.LoadAsync<T>(name, priority);
            var result = assetLoaded?.Get();
            if (result == null)
                throw new Exception($"Resource '{name}' not found");
            return result;
        }

        public void ReleaseCached(string lastLoadedName)
        {
        }

        public Match3EditorResourceProvider()
        {
            var bundleConfiguration = AssetsManagerFactory.CreateEditorAssetsManager(null);
            AssetsManager = bundleConfiguration.AssetsManager;
            _editorPrefabsDict = new Dictionary<string, GameObject>();
            _editorAudioContextDict = new Dictionary<string, AudioContextSettings>();
            _mixersDict = new Dictionary<string, AudioMixer>();
        }

        public GameObject GetPrefab(string name)
        {
            if (_prefabs.TryGetValue(name, out var prefab))
                return prefab;

            Debug.LogErrorFormat("Prefab {0} is not found in resource provider", name);
            return null;
        }

        public async UniTask LoadAsync()
        {
            var tasks = new List<UniTask>();

            foreach (AnimalSkin skin in Enum.GetValues(typeof(AnimalSkin)))
            {
                var spriteName = skin.ToSpriteName();
                if (spriteName == null) continue;
                tasks.Add(CacheAndLoadAsync<Sprite>(this, spriteName)
                    .ContinueWith(sp => _sprites[spriteName] = sp));
            }

            foreach (var kv in GatherPrefabTasks())
            {
                tasks.Add(kv);
            }

            await UniTask.WhenAll(tasks);
        }

        private IEnumerable<UniTask> GatherPrefabTasks()
        {
            var tasks = new List<UniTask>();

            var overlayNames = Enum.GetValues(typeof(CellOverlayType))
                                   .Cast<CellOverlayType>()
                                   .Where(t => t != CellOverlayType.None)
                                   .Select(t => t.ToPrefabName())
                                   .Distinct();
            foreach (var name in overlayNames)
            {
                tasks.Add(LoadResourceAsync(name, name, _prefabs));
            }

            const string effectPrefix = "EffectPrefabs/";
            foreach (FxType fx in Enum.GetValues(typeof(FxType)))
            {
                var pn = fx.ToPrefabName();
                if (!string.IsNullOrEmpty(pn))
                {
                    tasks.Add(LoadResourceAsync(pn, effectPrefix + pn, _prefabs));
                }
            }

            const string tilePrefix = "Prefabs/TileComponentPrefabs/";
            foreach (TileLayerState layer in Enum.GetValues(typeof(TileLayerState)))
            {
                if (layer == TileLayerState.None) continue;
                var pn = layer.ToPrefabName();
                tasks.Add(LoadResourceAsync(pn, tilePrefix + pn, _prefabs));
            }

            foreach (AnimalSkin skin in Enum.GetValues(typeof(AnimalSkin)))
            {
                var pn = skin.ToPrefabName();
                if (!string.IsNullOrEmpty(pn))
                {
                    tasks.Add(LoadResourceAsync(pn, GenericResKeys.AnimalFolder + pn, _prefabs));
                }
            }

            tasks.Add(LoadResourceAsync(
                Match3ResKeys.BonusTimeOverlayName,
                Match3ResKeys.BonusTimeOverlayPrefabName,
                _prefabs
            ));

            foreach (var contextKey in new[]
            {
                GenericResKeys.GenericSoundsContext,
                GenericResKeys.GenericVoiceoverContext,
                Match3ResKeys.Match3SoundsContext,
                Match3ResKeys.Match3VoiceoverContext
            })
            {
                tasks.Add(LoadResourceAsync(contextKey, contextKey, _editorAudioContextDict));
            }

            foreach (var mixerPath in GenericResKeys.MixerPaths)
            {
                tasks.Add(LoadResourceAsync(mixerPath, mixerPath, _mixersDict));
            }

            return tasks;
        }
        
        private async UniTask LoadResourceAsync<T>(
            string keyName,
            string assetName,
            Dictionary<string, T> resourceDict
        ) where T : UnityEngine.Object
        {
            try
            {
                var res = await CacheAndLoadAsync<T>(this, assetName);
                if (res == null)
                {
                    Debug.LogError($"Resource {keyName} not found");
                    return;
                }
                resourceDict[keyName] = res;
            }
            catch (Exception e)
            {
                Debug.LogError($"Exception loading '{assetName}' for key '{keyName}': {e}");
            }
        }
    }
}
#endif
