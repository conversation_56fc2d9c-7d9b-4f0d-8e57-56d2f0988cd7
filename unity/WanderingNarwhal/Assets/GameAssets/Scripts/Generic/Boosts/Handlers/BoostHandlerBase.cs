using System;
using BBB.CellTypes;
using BBB.Core;
using BBB.Match3.Logic;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.UI.Level.Input;
using Cysharp.Threading.Tasks;
using FBConfig;

namespace BBB.UI.Level.Scripts.Boosts.Handlers
{
    public abstract class BoostHandlerBase
    {
        public BoosterItem Type { get; }
        public string Id { get; private set; }
        public int Count => Inventory.GetBoosterAmount(Id);
        private IPlayerManager _playerManager;
        private IInventory Inventory => _playerManager.PlayerInventory;
        private bool _isTutorialLevel;
        protected IGridController GridController;
        protected IInputController InputController;
        protected GameController GameController;
        private IEventDispatcher _eventDispatcher;

        public event Action<bool> OnRefreshEvent;

        protected BoostHandlerBase(BoosterItem type)
        {
            Type = type;
        }

        public void Init(IGridController gridController,
            IInputController input,
            GameController gc,
            IPlayerManager playerManager, 
            IEventDispatcher eventDispatcher, 
            bool isTutorialLevel = false)
        {
            GridController = gridController;
            InputController = input;
            GameController = gc;
            _playerManager = playerManager;
            _isTutorialLevel = isTutorialLevel;
            _eventDispatcher = eventDispatcher;
        }

        public void RefreshTutorialStatus(bool isTutorialLevel)
        {
            _isTutorialLevel = isTutorialLevel;
        }
        
        public void Refresh(bool enableButton)
        {
            OnRefreshEvent?.Invoke(enableButton);
        }

        protected bool CheckApplicability(Coords inputCoords, BoosterItem item)
        {
            var cell = GameController.Grid.GetCell(inputCoords);

            if (cell == null)
                return false;

            if (item == BoosterItem.Shovel && cell.Tile.IsNull() && !cell.HasMultiSizeCellReference() && cell.IsNoneOf(CellState.HammerTarget)) return false;

            var tile = cell.Tile;

            if (cell.HasMultiSizeCellReferenceWithCellOverlay()) return false;

            if (!ReferenceEquals(tile, null) && tile.CheckApplicability(item, cell, GameController.Grid)
                || cell.CanCellReceiveDirectDamage(DamageSource.UsableBoost, GameController.Grid, item))
            {
                return true;
            }

            var mainCell = cell.GetMainCellReference(out _);
            return !ReferenceEquals(mainCell.Tile, null) && mainCell.Tile.CheckApplicability(item, mainCell, GameController.Grid)
                || mainCell.CanCellReceiveDirectDamage(DamageSource.UsableBoost, GameController.Grid, item);
        }

        public async UniTask<bool> Start(Coords inputCoords, InputState state)
        {
            return await SpendBooster(inputCoords, state, OnStart);
        }

        public async UniTask<bool> Down(Coords inputCoords, InputState state)
        {
            return await SpendBooster(inputCoords, state, OnDown);
        }
        
        public async UniTask<bool> Up(Coords inputCoords, InputState state)
        {
            return await SpendBooster(inputCoords, state, OnUp);
        }

        protected abstract UniTask<bool> OnStart(Coords inputCoords, InputState state);
        protected abstract UniTask<bool> OnDown(Coords inputCoords, InputState state);
        protected abstract UniTask<bool> OnUp(Coords inputCoords, InputState state);

        private async UniTask<bool> SpendBooster(Coords inputCoords, InputState state, Func<Coords, InputState, UniTask<bool>> canUseBusterAction)
        {
            if (!Inventory.HasBooster(Id))
            {
                return false;
            }
            var boosterSpent = await canUseBusterAction.Invoke(inputCoords, state);
            if (!boosterSpent || _isTutorialLevel)
                return boosterSpent;
            
            Inventory.SpendBooster(Id);
            var boosterSpentEvent = _eventDispatcher.GetMessage<BoosterSpentEvent>();
            boosterSpentEvent.Set(Id, GameController.Level.LevelUid, "level");
            _eventDispatcher.TriggerEvent(boosterSpentEvent);
            
            return true;
        }
        
        public static BoostHandlerBase MakeHandler(BoosterConfig config)
        {
            BoostHandlerBase result;
            
            try
            {
                var type = config.EnumName.TryParseToEnum<BoosterItem>();
                switch (type)
                {
                    case BoosterItem.Reshuffle:
                        result = new ReshufleBoostHandler();
                        break;
                    case BoosterItem.Shovel:
                        result = new ShovelBoostHandler();
                        break;
                    case BoosterItem.CreateLineBreaker:
                        result = new LinebreakerBoostHandler();
                        break;
                    case BoosterItem.Balloon:
                        result = new BalloonBoostHandler();
                        break;
                    case BoosterItem.Wind:
                        result = new WindBoostHandler();
                        break;
                    case BoosterItem.Vertical:
                        result = new VerticalBoostHandler();
                        break;
                    case BoosterItem.Horizontal:
                        result = new HorizontalBoostHandler();
                        break;
                    default:
                        result = new NoneBoostHandler();
                        break;
                }
            }
            catch(Exception ex)
            {
                BDebug.LogErrorFormat(LogCat.Match3, "Failed creating handler for {0} {1}", config.Uid, ex.Message);
                return new NoneBoostHandler();
            }
            
            result.Id = config.Uid;
            return result;
        }
    }
}