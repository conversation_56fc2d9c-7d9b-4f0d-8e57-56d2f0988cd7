using TMPro;
using UnityEngine;
using BBB.Core;
using DG.Tweening;
using UnityEngine.UI;

namespace BBB
{
    public class DiscoRushCollect : ActivePoolItem
    {
        [SerializeField] private TextMeshProUGUI _amountText;
        [SerializeField] private Animator _animator;
        private static readonly int Hide = Animator.StringToHash("Hide");
        

        public override void OnInstantiate()
        {
            if (!Initialized)
                _animator = gameObject.GetComponent<Animator>();
            base.OnInstantiate();
        }

        public override void OnRelease()
        {
            base.OnRelease();
            if (!Initialized)
                _animator.Rebind();
        }

        public void Setup(float totalDuration, int countOfTilesToDestroy)
        {
            const int startingScore = 1;
            DOTween.To(() => startingScore, UpdateText,
                    countOfTilesToDestroy, totalDuration)
                .OnComplete(() =>
                {
                    _amountText.SetText($"{+countOfTilesToDestroy}");
                    _animator.SetTrigger(Hide);
                });
        }

        private void UpdateText(int value)
        {
            _amountText.text = value.ToString();
        }

        public void OnAnimationEnded()
        {
            gameObject.Release();
        }
    }
}