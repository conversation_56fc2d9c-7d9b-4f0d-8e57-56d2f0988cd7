using System;
using BBB.Screens;
using UnityEngine;
using UnityEngine.UI;

namespace GameAssets.Scripts.Generic.Views
{
    public class ConfirmationModalViewPresenter : ModalsViewPresenter, IConfirmationModalViewPresenter
    {
        [SerializeField] private Button _confirmButton;
        [SerializeField] private LocalizedTextPro _confirmButtonText;
        [SerializeField] private Button _rejectButton;
        [SerializeField] private LocalizedTextPro _rejectButtonText;
        [SerializeField] private LocalizedTextPro _title;
        [SerializeField] private LocalizedTextPro _message;
        
        public event Action OnConfirmed;
        public event Action OnRejected;

        protected override void Awake()
        {
            base.Awake();
            _confirmButton.ReplaceOnClick(() => OnConfirmed.SafeInvoke());
            _rejectButton.ReplaceOnClick(() => OnRejected.SafeInvoke());
        }
        
        public void Setup(string titleId, string messageId, string confirmTextId = "CONFIRMATION_CONFIRM", string rejectTextId = "REJECT", object[] args = null)
        {
            _title.SetTextId(titleId);
            _message.SetTextId(messageId, args);
            _confirmButtonText.SetTextId(confirmTextId);
            _rejectButtonText.SetTextId(rejectTextId);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            OnConfirmed = null;
            OnRejected = null;
        }
    }
}
