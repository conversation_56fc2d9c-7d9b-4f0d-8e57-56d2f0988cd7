using BBB;
using DG.Tweening;
using UnityEngine;

namespace GameAssets.Scripts.Generic
{
    public class OutOfMovesMatch3BoardViewer: BbbMonoBehaviour
    {
        [SerializeField] private RectTransform _scrim1;
        [SerializeField] private RectTransform _scrim2;
        [SerializeField] private float _yOffset = 27f;
        [SerializeField] private float _originDistance = 1000f;
        [SerializeField] private float _showDuration = 0.25f;
        [SerializeField] private Ease _showScrimEase = Ease.OutSine;
        [SerializeField] private float _hideDuration = 0.25f;
        [SerializeField] private Ease _hideScrimEase = Ease.InSine;
        [field:SerializeField] public RectTransform InverseMaskTransform { get; private set; }

        public void ShowScrimAtPositions(Camera levelCamera, float y1, float y2)
        {
            _scrim1.anchoredPosition = new Vector2(0f, y1 + _originDistance);
            var destY1 = GetAnchoredY(levelCamera, y1+ _yOffset);
            DOTween.To(() => _scrim1.anchoredPosition.y,
                    y => _scrim1.anchoredPosition = new Vector2(0f, y),
                    destY1,
                    _showDuration)
                .SetEase(_showScrimEase);

            _scrim2.anchoredPosition = new Vector2(0f, y2 - _originDistance);
            var destY2 = GetAnchoredY(levelCamera, y2 - _yOffset);
            DOTween.To(() => _scrim2.anchoredPosition.y,
                    y => _scrim2.anchoredPosition = new Vector2(0f, y),
                    destY2,
                    _showDuration)
                .SetEase(_showScrimEase);
        }

        public void HideScrims(System.Action onDone)
        {
            DOTween.To(() => _scrim1.anchoredPosition.y,
                    y => _scrim1.anchoredPosition = new Vector2(0f, y),
                    _originDistance,
                    _hideDuration)
                .SetEase(_hideScrimEase);

            DOTween.To(() => _scrim2.anchoredPosition.y,
                    y => _scrim2.anchoredPosition = new Vector2(0f, y),
                    -_originDistance,
                    _hideDuration)
                .SetEase(_hideScrimEase)
                .OnComplete(onDone.SafeInvoke);
        }

        private float GetAnchoredY(Camera levelCamera, float y)
        {
            var screenPos = RectTransformUtility.WorldToScreenPoint (levelCamera, new Vector3(0f, y, 0f));
            RectTransformUtility.ScreenPointToLocalPointInRectangle (transform.RectTransform(), screenPos, null, out var anchoredPos);
            return anchoredPos.y;
        }
    }
}
