using System;
using System.Collections.Generic;
using BBB.Core.ResourcesManager;
using BBB.DI;
using BBB.Match3.Renderer;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.UI.Level
{
    public class Match3ResourceProvider : ResourceProviderBase, IMatch3SharedResourceProvider
    {
        private Match3ResourceHelper _helper;
        private IConfig _config;
        private LevelHolder _levelHolder;
        private SpawnerSettingsManager _spawnerSettingsManager;

        AudioContextSettings IMatch3SharedResourceProvider.GetAudioContext(string name)
        {
            return (AudioContextSettings)PreloadedResourcePack.GetResource(name);
        }

        Sprite IMatch3SharedResourceProvider.GetSprite(string name)
        {
            return (Sprite)PreloadedResourcePack.GetResource(name);
        }

        GameObject IMatch3SharedResourceProvider.GetPrefab(string name)
        {
            return (GameObject)PreloadedResourcePack.GetResource(name);
        }
        
        protected override void OnInitializeByContext(IContext context)
        {
            _helper = context.Resolve<Match3ResourceHelper>();
            _config = context.Resolve<IConfig>();
            _spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
            _levelHolder = context.Resolve<LevelHolder>();
        }

        protected override void OnReleaseByContext(IContext context)
        {
            _config = null;
        }

        public override void CacheResources(IResourceCache cache)
        {
            var level = _levelHolder.level;
            if (level != null)
            {
                var resourceInfos = _helper.GetTileResourceInfos(level, _spawnerSettingsManager.SpawnerSettings);

                const string tcPrefix = "Prefabs/TileComponentPrefabs/";
                var processedPrefabNames = new HashSet<string>();

                foreach (var info in resourceInfos)
                {
                    var prefabName = info.PrefabName;

                    if (processedPrefabNames.Add(prefabName))
                    {
                        PreloadedResourcePack.AddResourceToPreloadWithKey<GameObject>(this, prefabName, tcPrefix + prefabName, cache);
                    }
                }

                var animalPrefab = level.AnimalSkin.ToPrefabName();

                if (animalPrefab != null)
                {
                    foreach (var ri in resourceInfos)
                    {
                        if (ri.State != TileLayerState.Animal) continue;
                        
                        PreloadedResourcePack.AddResourceToPreloadWithKey<GameObject>(this, animalPrefab, GenericResKeys.AnimalFolder + animalPrefab, cache);
                        break;
                    }
                }
            
                const string epPrefix = "EffectPrefabs/";
                var fxTypes = _helper.GetUsedFxTypes(level, _config, _spawnerSettingsManager.SpawnerSettings);
                
                processedPrefabNames.Clear();

                foreach (var fxType in fxTypes)
                {
                    var prefabName = fxType.ToPrefabName();

                    if (!prefabName.IsNullOrEmpty() && processedPrefabNames.Add(prefabName))
                    {
                        PreloadedResourcePack.AddResourceToPreloadWithKey<GameObject>(this, prefabName, epPrefix + prefabName, cache);
                    }
                }

                if (level.Goals.GetGoalValue(GoalType.Animal) > 0)
                {
                    var spriteName = level.AnimalSkin.ToSpriteName();
                    PreloadedResourcePack.AddResourceToPreloadWithKey<Sprite>(this, spriteName, spriteName, cache);
                }
            }
            
            foreach (CellOverlayType cellOverlayType in Enum.GetValues(typeof(CellOverlayType)))
            {
                if (cellOverlayType == CellOverlayType.None) continue;

                var prefabName = cellOverlayType.ToPrefabName();
                PreloadedResourcePack.AddResourceToPreload<GameObject>(this, prefabName, cache);
            }
            
            PreloadedResourcePack.AddResourceToPreloadWithKey<GameObject>(this, Match3ResKeys.BonusTimeOverlayName, Match3ResKeys.BonusTimeOverlayPrefabName, cache);
            PreloadedResourcePack.AddResourceToPreload<AudioContextSettings>(this, Match3ResKeys.Match3SoundsContext, cache);
            PreloadedResourcePack.AddResourceToPreload<AudioContextSettings>(this, Match3ResKeys.Match3VoiceoverContext, cache);
        }

        protected override void OnDisposeResources()
        {
            foreach (var audioContextSettings in GetAllPreloaded<AudioContextSettings>())
            {
                AudioProxy.RemoveContext(audioContextSettings);
            }
        }
    }

    public static class Match3ResKeys
    {        
        public const string BonusTimeOverlayName = "BonusTimeOverlay";
        public static readonly string BonusTimeOverlayPrefabName = $"Prefabs/{BonusTimeOverlayName}";

        public const string Match3SoundsContext = "Audio/Match3SoundsContextSettings";
        public const string Match3VoiceoverContext = "Audio/Match3VoiceoverContextSettings";
        public const string ProfileCrown = "Prefabs/ProfileCrown";
    }
}