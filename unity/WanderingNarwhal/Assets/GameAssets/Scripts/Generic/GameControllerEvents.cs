using System;
using System.Collections.Generic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using FBConfig;
using GameAssets.Scripts.Messages;

namespace BBB
{
    public class LevelResultPredicted : Message<LevelOutcome>, IResettableEvent
    {
        public bool AllowRecording;
        public ScreenType ScreenType;
        public ILevel level;
        
        public void Reset()
        {
            AllowRecording = default;
            level = null;
        }
    }

    public class LevelEndedEvent : IResettableEvent
    {
        public LevelOutcome LevelOutcome { get; set; }
        public bool AllowShowOutOfMovesModal { get; set; }
        public int ShuffleCount { get; set; }
        public void Reset()
        {
            LevelOutcome = default;
            AllowShowOutOfMovesModal = default;
            ShuffleCount = default;
        }
    }

    public class RemainingMovesChanged : Message<int>
    {
    }

    public class EquippedAutoBoostAppliedEvent : Message<string, Coords?, object, bool>
    {
    }

    public class PendingAutoBoosterAddedEvent : Message<List<AutoBoostInstance>, Action<string, Coords?, object>>
    {
    }

    public class InventoryBoosterSpentEvent : Message<string>
    {
    }

    public class InventoryBoosterUpdatedEvent : Message<string>
    {
    }
    
    public class BoosterSpentEvent : Message<string, string, string>
    {
    }

    public class GachaSpinPurchasedEvent : Message<bool, Price>
    {
    }

    public class FirstLevelMoveEvent : IEvent
    {
    }

    public class LevelWonEvent : Message<int>
    {
    }

    public class TileCollectedEvent : Message<Tile>
    {
    }

    public class BoosterUsedEvent : Message<BoosterItem>
    {
    }

    public class PowerupCreatedEvent : Message<Tile>
    {
    }
}