#if UNITY_EDITOR
using System;
using BBB.DI;
using BBB.M3Editor;
using BBB.Match3.Debug;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;
using BBB.Match3.Systems.GoalsService;
using BBB.Social;
using BBB.Testers.Mocks;
using BBB.UI.Level.Scripts.Boosts;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.UI
{
    public class DebugLevelController : LevelControllerBase
    {
        private M3Editor.M3Editor _m3Editor;
        private ILocalizationManager _localizationManager;
        
        public override bool IsEditor => true;
        public SuperBoostPanelController SuperBoostPanelController => SuperBoostPanel;
        public IPlayerManager PlayerManager => _playerManager;
        public IBoostButtonsController BoostButtonsController => _boostButtons;

        public TileRevealer GetTileRevealer() => TileRevealer;

        public override void Init(IContext previousContext)
        {
            M3Debug.Disabled = false;
            Debug.unityLogger.logEnabled = true;
            base.Init(previousContext);

            _localizationManager = previousContext.Resolve<ILocalizationManager>();

            var proxy = GetComponent<LevelControllerReferenceProxy>();
            proxy.LevelTitle.Setup(_localizationManager, _levelHolder.level);
        }

        public override void OnShow()
        {
            base.OnShow();
            GameController.Refresh();
            _m3Editor.OnShow();
        }

        protected override void RegisterSpecificServices(UnityContext context, IContext previousContext)
        {
            _m3Editor = FindObjectOfType<M3Editor.M3Editor>();
            context.AddServiceToRegister<M3Editor.M3Editor>(_m3Editor);
            context.AddServiceToRegister<IAssistParamsProvider>(_m3Editor);
            context.AddServiceToRegister<HeuristicMovePicker>(new HeuristicMovePicker());
            context.AddServiceToRegister<ITrainingSystem>(new MinimumSearchTrainingSystem());
            context.AddServiceToRegister<AutoBruteSystem>(new AutoBruteSystem());
            context.AddServiceToRegister<BalanceReporter>(new BalanceReporter());
            var challengeTriviaManager = new EditorLevelControllerLauncher.ChallengeTriviaManagerStub();
            context.AddServiceToRegister<ChallengeTriviaManager>(challengeTriviaManager);
            context.AddServiceToRegister<M3EditorLevelItemController>(new M3EditorLevelItemController(_m3Editor));
            context.AddServiceToRegister<M3EditorLevelBoostersController>(new M3EditorLevelBoostersController(_m3Editor));
            context.AddServiceToRegister<ILocationManager>(new MockLocationManager());
            context.AddServiceToRegister<ILevelAnalyticsReporter>(new EditorLevelControllerLauncher.AnalyticsReporterStub());
        }

        public async UniTask RefreshForGrid(Grid grid)
        {
            _levelHolder.level.Grid = grid;
            grid.SortCells();
            var tileController = Context.Resolve<TileController>();
            tileController.Clear();

            foreach (var cell in grid.Cells)
            {
                if (!ReferenceEquals(cell.Tile, null))
                {
                    tileController.UpdateCount(cell);
                }
            }

            Context.Resolve<GoalsSystem>().RefreshForLevel(GameController.Level);
            var gameEventManagersCollection = Context.Resolve<GameEventMatch3ManagersCollection>();
            Context.Resolve<TilesResources>().RefreshForLevel(
                GameController.Level,
                Context.Resolve<IGameEventManager>(),
                gameEventManagersCollection,
                Context.Resolve<IGameEventResourceManager>(),
                Context.Resolve<IScreensBuilder>());
            Context.Resolve<TileResourceSelector>().RefreshForLevel(GameController.Level);
            await GoalPanel.FillGoalsAsync(GameController.Level);

            Context.Resolve<HeuristicMovePicker>().RefreshForLevel(GameController.Level);
            foreach (var tileView in tileController.GetAllTileViews())
            {
                tileView.UpdatePositionDependentProperties();
            }
        }

        public override void SetSuperDiscoBallValue(float updatedValue)
        {
            base.SetSuperDiscoBallValue(updatedValue);
            var randomState = RandomSystem.GetRandomGenerationValues();
            _m3Editor.LevelAnalyticsReporter.RegisterSuperDiscoBallValueUpdate(randomState, updatedValue);
        }

        protected override void OnLevelResultPredicted(LevelResultPredicted ev)
        {

        }

        [ContextMenu("Force Refresh Grid")]
        private void DebugUpdateView()
        {
            var levelHolder = Context.Resolve<LevelHolder>();
            var level = levelHolder.level;
            var grid = level.Grid;
            var tileController = Context.Resolve<TileController>();
            tileController.Update(grid, force: true);
        }

        protected override void OnLevelEnded(LevelEndedEvent ev)
        {
            Debug.Log("Game ended. Level outcome " + Enum.GetName(typeof(LevelOutcome), ev.LevelOutcome));
        }

        public override void ReleaseByContext(IContext context)
        {
            base.ReleaseByContext(context);
            Destroy(gameObject);
        }

        protected override void ReleaseScreenContext()
        {
        }
    }
}
#endif
