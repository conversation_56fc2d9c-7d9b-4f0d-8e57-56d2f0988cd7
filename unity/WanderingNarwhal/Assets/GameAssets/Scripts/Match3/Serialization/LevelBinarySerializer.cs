using System;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Renderer;
using BBB.UI.Level;
using Core.Configs;
using GameAssets.Scripts.Match3.Logic;
using PBLevel;
using UnityEngine;

namespace BBB.Match3
{
    public static class PBModelExtensions
    {
        private static PBCoords ToPbCoords(this Coords coords)
        {
            return new PBCoords { X = coords.X, Y = coords.Y };
        }

        private static Coords ToCoords(this PBCoords pbCoords)
        {
            return new Coords(pbCoords.X, pbCoords.Y);
        }

        private static Tile ToTile(this PBTile pbTile, Grid grid)
        {
            return pbTile == null ? null : TileFactory.CreateTile(grid, pbTile);
        }

        private static PBTile ToPBTile(this Tile tile)
        {
            if (ReferenceEquals(tile, null))
                return null;

            var pbTile = new PBTile
            {
                Asset = (int) tile.Asset,
                Kind = (int) tile.Kind
            };

            foreach (var modValue in TileStateExtensions.ModStates)
            {
                if (tile.IsAnyOf(modValue))
                    pbTile.Mod |= (ulong)modValue;
            }

            pbTile.TileParams = tile.GetParamsDto().ToPBParams();

            return pbTile;

        }

        private static List<PBTileParam> ToPBParams(this List<TileParam> tileParams)
        {
            if (tileParams == null)
                return null;

            var result = new List<PBTileParam>();

            foreach (var tileParam in tileParams)
            {
                var pbParam = new PBTileParam
                {
                    Param = (int) tileParam.Param,
                    Value = tileParam.Value
                };

                result.Add(pbParam);
            }

            return result;
        }

        public static List<TileParam> ToParams(this List<PBTileParam> pbParams)
        {
            if (pbParams == null)
                return null;

            var result = new List<TileParam>();

            foreach(var pbParam in pbParams)
                if(pbParam == null)
                    throw new Exception("Tile param is null");
                else
                {
                    var param = new TileParam((TileParamEnum)pbParam.Param, pbParam.Value);
                    result.Add(param);
                }

            return result;
        }

        public static Cell ToCell(this PBCell pbCell, Grid grid)
        {
            if (pbCell == null)
                throw new Exception("Cell argument is null");

            var coords = new Coords(pbCell.X, pbCell.Y);
            var resultCell = grid.CreateCell(coords);
            resultCell.BackgroundCount = pbCell.BackgroundCount;
            resultCell.IvyCount = pbCell.IvyCount;
            resultCell.IceBarStatus = pbCell.IceBarStatus;
            resultCell.MetalBarStatus = pbCell.MetalBarStatus;
            resultCell.Add((CellState)pbCell.State);
            resultCell.SpawnerUid = pbCell.SpawnerConfigIndex;
            resultCell.SizeX = pbCell.SizeX;
            resultCell.SizeY = pbCell.SizeY;
            resultCell.TntCount = pbCell.TntCount;
            resultCell.TntKind = (TileKinds)pbCell.TntTileKind;
            resultCell.TntTarget = (TntTargetType)pbCell.TntTarget;
            if (resultCell.State == CellState.Tnt)
            {
                UnityEngine.Debug.Log("Set cell sizeX: " + resultCell.SizeX);
            }
            
            if(resultCell.IvyCount > 0  && !resultCell.IsAnyOf(CellState.Ivy))
            {
                resultCell.Add(CellState.Ivy);
            }

            if (resultCell.IvyCount == 0 && resultCell.IsAnyOf(CellState.Ivy))
            {
                resultCell.IvyCount = 1;
            }

            if (!resultCell.IceBarStatus && resultCell.IsAnyOf(CellState.IceBar))
            {
                resultCell.IceBarStatus = true;
            }
            
            if (!resultCell.MetalBarStatus && resultCell.IsAnyOf(CellState.MetalBar))
            {
                resultCell.MetalBarStatus = true;
            }

            if (resultCell.BackgroundCount > 0 && !resultCell.IsBackState() && !resultCell.IsPetal())
            {
                switch (resultCell.BackgroundCount)
                {
                    // Cell has background value, but not background state, which should be present.
                    // BackgroundCount corresponding to Back state and Ivy state. By default Back state is selected. -VK
                    case 1:
                        resultCell.Add(CellState.BackOne);
                        break;
                    case 2:
                        resultCell.Add(CellState.BackDouble);
                        break;
                }
            }

            if (resultCell.IceBarStatus && !resultCell.IsAnyOf(CellState.IceBar))
            {
                resultCell.Add(CellState.IceBar);
            }
            
            if (resultCell.MetalBarStatus && !resultCell.IsAnyOf(CellState.MetalBar))
            {
                resultCell.Add(CellState.MetalBar);
            }
            
            resultCell.Walls = pbCell.CellWalls == 0 ? null : new CellWalls((CardinalDirections)pbCell.CellWalls);
            resultCell.InvisibleWalls = pbCell.CellInvisibleWalls == 0 ? null : new CellWalls((CardinalDirections)pbCell.CellInvisibleWalls);
            resultCell.DestructibleWalls = ToDestructibleWalls(pbCell.CellDestructibleWalls);

            if (resultCell.HasDestructibleWall())
            {
                resultCell.Add(CellState.DestructibleWall);
            }
            
            if (pbCell.Tile != null)
            {
                resultCell.AddTile(pbCell.Tile.ToTile(grid));
            }

            return resultCell;
        }

        private static DestructibleWalls ToDestructibleWalls(IReadOnlyList<PBDestructibleWalls> pbDestructibleWalls)
        {
            if (pbDestructibleWalls == null) return null;
            var destructibleWalls = new DestructibleWalls
            {
                DestructibleWall = new DestructibleWall[DestructibleWalls.NumberOfWalls]
            };

            for (var i = 0; i < DestructibleWalls.NumberOfWalls; i++)
            {
                destructibleWalls.DestructibleWall[i] = new DestructibleWall
                {
                    Directions = DestructibleWalls.IndexToCardinal(pbDestructibleWalls[i].CardinalDirection),
                    Count = pbDestructibleWalls[i].Count
                };
            }

            return destructibleWalls;
        }
        
        private static PBDestructibleWalls[] ToDestructibleWalls(DestructibleWalls destructibleWalls)
        {
            if (destructibleWalls == null)
                return null;

            var pbDestructibleWalls = new PBDestructibleWalls[4];

            for (var i = 0; i < 4; i++)
            {
                pbDestructibleWalls[i] = new PBDestructibleWalls
                {
                    CardinalDirection = DestructibleWalls.CardinalToIndex(destructibleWalls.DestructibleWall[i].Directions),
                    Count = destructibleWalls.DestructibleWall[i].Count
                };
            }
            
            return pbDestructibleWalls;
        }
        

        public static PBCell ToPBCell(this Cell cell)
        {
            if (cell == null)
                throw new Exception("Cell argument is null");

            var resultPbCell = new PBCell();
            resultPbCell.X = cell.Coords.X;
            resultPbCell.Y = cell.Coords.Y;
            resultPbCell.BackgroundCount = cell.BackgroundCount;
            resultPbCell.IvyCount = cell.IvyCount;
            resultPbCell.IceBarStatus = cell.IceBarStatus;
            resultPbCell.MetalBarStatus = cell.MetalBarStatus;
            resultPbCell.State = (int)cell.State;
            resultPbCell.CellWalls = cell.Walls == null ? 0 : (int)cell.Walls.Directions;
            resultPbCell.CellInvisibleWalls = cell.InvisibleWalls == null ? 0 : (int)cell.InvisibleWalls.Directions;
            resultPbCell.CellDestructibleWalls = ToDestructibleWalls(cell.DestructibleWalls);
            resultPbCell.Tile = cell.Tile.ToPBTile();
            resultPbCell.SpawnerConfigIndex = cell.SpawnerUid;
            resultPbCell.SizeX = cell.SizeX;
            resultPbCell.SizeY = cell.SizeY;
            resultPbCell.TntCount = cell.TntCount;
            resultPbCell.TntTileKind = (int)cell.TntKind;
            resultPbCell.TntTarget = (int)cell.TntTarget;
            return resultPbCell;
        }
    }

    public static class LevelBinarySerializer
    {
        private static ProtobufSerializer _protobufSerializer;

        public static void SetWrapper(ProtobufSerializer protobufSerializer)
        {
            _protobufSerializer = protobufSerializer;
        }

        public static byte[] Serialize(ILevel level)
        {
            if(level == null)
                throw new Exception("Null Level argument");

            var pbLevel = new PBLevel.PBLevel();

            if(level.Grid == null)
                throw new Exception("Grid not found in the Level");

            if(level.Grid.Cells == null)
                throw new Exception("Cells not found in the Level");

            pbLevel.Grid = new PBGrid
            {
                Cells = new List<PBCell>()
            };

            foreach (var cell in level.Grid.Cells)
            {
                pbLevel.Grid.Cells.Add(cell.ToPBCell());
            }

            pbLevel.LitterSkin = (int)level.LitterSkin;
            pbLevel.StickerSkin = (int)level.StickerSkin;

            pbLevel.UsedKindsTypes = LevelHelper.TileKindsToInt(level.UsedKinds);
            var serializer = _protobufSerializer;
            var data = serializer.SerializeToBytes<PBLevel.PBLevel, LevelSerializer>(pbLevel);
            return data;
        }

        public static Level Deserialize(byte[] data)
        {
            if (data == null || data.Length == 0)
                throw new Exception("Null or empty data argument");
            var serializer = _protobufSerializer;
            var pbLevel = serializer.DeserializeBytes<PBLevel.PBLevel, LevelSerializer>(data);

            var level = new Level();
            var pbCells = pbLevel.Grid.Cells;

            if (pbCells == null || pbCells.Count == 0)
            {
                level.Grid = null;
            }
            else
            {
                var maxX = int.MinValue;
                var maxY = int.MinValue;

                foreach (var cell in pbCells)
                {
                    if (cell.X > maxX)
                    {
                        maxX = cell.X;
                    }

                    if (cell.Y > maxY)
                    {
                        maxY = cell.Y;
                    }
                }

                level.Grid = new Grid(maxX + 1, maxY + 1);

                foreach (var pbCell in pbCells)
                {
                    pbCell.ToCell(level.Grid);
                }

                level.Grid.SortCells();
            }

            level.UsedKinds = LevelHelper.TileKindsFromInt(pbLevel.UsedKindsTypes);
            level.LitterSkin = (LitterSkin)pbLevel.LitterSkin;
            level.StickerSkin = (StickerSkin)pbLevel.StickerSkin;

            level.Goals = new GoalState();
            level.AssetHash = Hash128.Compute(data);
            return level;
        }
    }
}
