using BBB;
using BBB.Core;
using JetBrains.Annotations;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    public class SingleAnimationEffect : ActivePoolItem
    {
        [SerializeField] bool useEnableDisableVersion = false;
        private Animation _animation;

        private void Awake()
        {
            _animation = GetComponent<Animation>();
        }

        public override void OnInstantiate()
        {
            base.OnInstantiate();
            if (useEnableDisableVersion)
            {
                UnregisterComponent(_animation);
            }
        }

        void OnValidate()
        {
            if (useEnableDisableVersion)
            {
                if (!_animation)
                {
                    _animation = GetComponent<Animation>();
                }
                if (_animation)
                {
                    _animation.enabled = false;
                }
            }
        }

        public virtual void ApplyParameters(FxOptionalParameters prm)
        {
        }

        public override void OnSpawn()
        {
            if (this == null) return;
            if (useEnableDisableVersion && _animation)
            {
                _animation.enabled = true;
            }
            base.OnSpawn();
        }

        public override void OnRelease()
        {
            if (this == null) return;
            base.OnRelease();
            if (_animation)
            {
                _animation.Rewind();
            }
        }

        [UsedImplicitly]
        void DisableEffect()
        {
            if (_animation)
            {
                _animation.enabled = false;
            }
        }
    }
}