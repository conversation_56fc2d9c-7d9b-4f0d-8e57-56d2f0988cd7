using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using BBB;
using BBB.Core;
using BBB.Match3;
using BBB.Match3.Renderer;
using BebopBee.Core;
using BebopBee.UnityEngineExtensions;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using UnityEngine;
using Random = UnityEngine.Random;

namespace GameAssets.Scripts.Match3.Logic
{
    public class PropellerComboFlightEffect : ActivePoolItem
    {
        private static readonly int RotationIntro = Animator.StringToHash("RotationIntro");

        const string LayerNameOverlay = "Overlay";
        const string LayerNameDefault = "Default";
        
        [SerializeField] private Transform _propellerTransform;
        [SerializeField] private Transform _bombTransform;
        [SerializeField] private Transform _rowBreakerTransform;
        [SerializeField] private Transform _columnBreakerTransform;

        [SerializeField] private PropellerLayerRenderer _propellerLayerRenderer;
        [SerializeField] private BombRenderer _bombRenderer;
        [SerializeField] private LineBreakerRenderer _rowBreakerRenderer;
        [SerializeField] private LineBreakerRenderer _columnBreakerRenderer;

        [SerializeField] private Transform _leftHolder;
        [SerializeField] private Transform _rightHolder;
        [SerializeField] private Transform _hinge;

        [SerializeField] private List<Transform> _transformsToResetOnLaunch;

        [SerializeField] private Animator _animator;

        private ISpecialTileRenderer _leftRenderer;
        private ISpecialTileRenderer _rightRenderer;

        private readonly Dictionary<Transform, IPoolItem> _transformToPool = new Dictionary<Transform, IPoolItem>();

        //animated by the clips
        [SerializeField]
        private bool _sortLeftCloser;

        private IEnumerable<Transform> AllTransforms
        {
            get
            {
                yield return _propellerTransform;
                yield return _bombTransform;
                yield return _rowBreakerTransform;
                yield return _columnBreakerTransform;
            }
        }

        private readonly List<Transform> _transformsToRelease = new List<Transform>();
        private readonly List<Tween> _tweens = new List<Tween>();
        
        private Action _callback;
        private Coroutine _coroutine;

        private (Transform tf, ISpecialTileRenderer renderer) SelectTransformAndRenderer(TileSpeciality spec)
        {
            switch (spec)
            {
                case TileSpeciality.Propeller:
                    return (_propellerTransform, _propellerLayerRenderer);
                case TileSpeciality.Bomb:
                    return (_bombTransform, _bombRenderer);
                case TileSpeciality.RowBreaker:
                    return (_rowBreakerTransform, _rowBreakerRenderer);
                case TileSpeciality.ColumnBreaker:
                    return (_columnBreakerTransform, _columnBreakerRenderer);
            }
            
            BDebug.LogError(LogCat.General, "Transform not found for spec " + spec);

            return (null,null);
        }

        public void LaunchRotation(Vector3 propellerWorldPos, Vector3 otherWorldPos,
            TileSpeciality propellerSpec, TileSpeciality otherSpec, Vector3 propellerRendererGlobalScale,
            Vector3 otherRendererGlobalScale, CardinalDirections dir,
            PropellerComboFlightEffectSettings settings)
        {
            if(_transformsToResetOnLaunch != null)
                foreach (var tfToReset in _transformsToResetOnLaunch)
                {
                    tfToReset.localPosition = Vector3.zero;
                    tfToReset.localScale = Vector3.one;
                    tfToReset.localRotation = Quaternion.identity;
                }

            foreach(var someTf in AllTransforms)
            {
                if (_transformToPool.TryGetValue(someTf, out IPoolItem poolItem))
                    poolItem.OnRelease();
                else
                    someTf.gameObject.SetActive(false);
            }
            
            var propellerTuple = SelectTransformAndRenderer(propellerSpec);
            var otherTuple = SelectTransformAndRenderer(otherSpec);

            if (propellerTuple.tf == null || propellerTuple.renderer == null
                                          || otherTuple.tf == null || otherTuple.renderer == null)
            {
                return;
            }

            bool horizontal = dir == CardinalDirections.E || dir == CardinalDirections.W;
            bool propellerOnTheLeft = dir == CardinalDirections.E || dir == CardinalDirections.S;

            _hinge.localRotation = Quaternion.Euler(0f, 0f, horizontal ? 0f : 90f);
            _leftHolder.rotation = Quaternion.identity;
            _rightHolder.rotation = Quaternion.identity;

            var propellerHolder = propellerOnTheLeft ? _leftHolder : _rightHolder;
            var otherHolder = propellerOnTheLeft ? _rightHolder : _leftHolder;

            _leftRenderer = propellerOnTheLeft ? propellerTuple.renderer : otherTuple.renderer;
            _rightRenderer = propellerOnTheLeft ? otherTuple.renderer : propellerTuple.renderer;

            propellerHolder.position = propellerWorldPos;
            otherHolder.position = otherWorldPos;
            
            propellerTuple.tf.SetParent(propellerHolder, true);
            propellerTuple.tf.localPosition = Vector3.zero;
            otherTuple.tf.SetParent(otherHolder, true);
            otherTuple.tf.localPosition = Vector3.zero;
            
            if(otherSpec == TileSpeciality.ColumnBreaker)
                otherHolder.rotation = Quaternion.Euler(0f, 0f, 90f);
            
            propellerTuple.tf.SetGlobalScale(propellerRendererGlobalScale);
            otherTuple.tf.SetGlobalScale(otherRendererGlobalScale);

            if (_transformToPool.TryGetValue(propellerTuple.tf, out IPoolItem propellerPoolItem))
                propellerPoolItem.OnSpawn();
            else
                propellerTuple.tf.gameObject.SetActive(true);

            if (_transformToPool.TryGetValue(otherTuple.tf, out IPoolItem otherPoolItem))
                otherPoolItem.OnSpawn();
            else
                otherTuple.tf.gameObject.SetActive(true);
            
            _transformsToRelease.Clear();
            _transformsToRelease.Add(propellerTuple.tf);
            _transformsToRelease.Add(otherTuple.tf);
            
            propellerTuple.renderer.PlaySwap();
            otherTuple.renderer.PlaySwap();
            
            _animator.Play(RotationIntro);

            var distance = _rightHolder.localPosition.x - _leftHolder.localPosition.x;

            AnimateHolder(_leftHolder, settings.IntroCurveForLeftHolderX, 
                settings.IntroCurveForLeftHolderY, 
                settings.LoopCurveForLeftHolderX, 
                settings.LoopCurveForLeftHolderY, 
                distance, settings.IntroTime, settings.LoopTime);

            AnimateHolder(_rightHolder, settings.IntroCurveForRightHolderX, 
                settings.IntroCurveForRightHolderY, 
                settings.LoopCurveForRightHolderX, 
                settings.LoopCurveForRightHolderY, 
                distance, settings.IntroTime, settings.LoopTime);

            _coroutine = StartCoroutine(UpdateRoutine());
        }

        private IEnumerator UpdateRoutine()
        {
            while (true)
            {

                if (_sortLeftCloser)                                                 
                {
                    if(_leftRenderer != null)
                        _leftRenderer.SortingLayer = SortingLayer.NameToID(LayerNameOverlay);
                    if(_rightRenderer != null)
                        _rightRenderer.SortingLayer = SortingLayer.NameToID(LayerNameDefault);
                }
                else
                {
                    if(_rightRenderer != null)
                        _rightRenderer.SortingLayer = SortingLayer.NameToID(LayerNameOverlay);

                    if(_leftRenderer != null)
                        _leftRenderer.SortingLayer = SortingLayer.NameToID(LayerNameDefault); 
                }

                yield return null;
            }
        }

        private Tween AnimateHolder(Transform holder, AnimationCurve introXCurve, AnimationCurve introYCurve, AnimationCurve loopXCurve,
            AnimationCurve loopYCurve, float distance, float introTime, float loopTime)
        {
            var initialPos = holder.localPosition;

            var sequenceX = DOTween.Sequence();
            var introX = holder.DOLocalMoveX(distance, introTime)
                .SetEase(introXCurve).SetRelative().OnComplete(() =>
                {
                    var lPos = holder.localPosition;
                    lPos.x = initialPos.x;
                    holder.localPosition = lPos;
                });

            sequenceX.Append(introX);
            _tweens.Add(sequenceX);
            _tweens.Add(introX);

            for (int i = 0; i < 10; i++)
            {
                var loopX = holder.DOLocalMoveX(distance, loopTime)
                    .SetEase(loopXCurve).SetRelative().SetLoops(int.MaxValue);
                sequenceX.Append(loopX);
                _tweens.Add(loopX);
            }

            var sequenceY = DOTween.Sequence();
            var introY = holder.DOLocalMoveY(distance, introTime)
                .SetEase(introYCurve).SetRelative().OnComplete(() =>
                {
                    var lPos = holder.localPosition;
                    lPos.y = initialPos.y;
                    holder.localPosition = lPos;
                });

            sequenceY.Append(introY);
            _tweens.Add(sequenceY);
            _tweens.Add(introY);

            for (int i = 0; i < 10; i++)
            {
                var loopY = holder.DOLocalMoveY(distance, loopTime)
                    .SetEase(loopYCurve).SetRelative();

                sequenceY.Append(loopY);
                _tweens.Add(loopY);
            }

            return sequenceX;
        }
        
        public async UniTask LaunchFlight(Coords propellerCoord, Coords otherCoord,
            TrajectoryMultiplierDeterminer multiplierDeterminer,
            Coords targetCoord, PropellerComboFlightEffectSettings settings,
            Action callback, IGridController gridController, int targetTileId, CancellationToken cancellationToken = default)
        {
            var targetLocalPosition = targetCoord == Coords.OutOfGrid
                ? transform.localPosition.ToXY()
                : gridController.ToLocalPosition(targetCoord);
            
            _callback = callback;
            
            Tween hoverTween = null;
            var tf = transform;
            var easingNeedsToBeChanged = false;
            const float easingChangeTime = 0;
            
            try
            {
                var tile = gridController.GetTile(targetCoord);

                if (targetTileId != 0 && tile != null && tile.Id != targetTileId)
                {
                    if (cancellationToken == default)
                    {
                        const int maxWaitTime = 3;
                        cancellationToken = new CancellationTokenSource(TimeSpan.FromSeconds(maxWaitTime)).Token;
                    }

                    var perimeter = gridController.PerimeterTransform;
                    var worldCorners = new Vector3[4];
                    perimeter.GetWorldCorners(worldCorners);
                    
                    for (var i = 0; i < worldCorners.Length; i++)
                    {
                        worldCorners[i].z = tf.position.z;
                    }
                    
                    const float hoverDuration = 5f;
                    hoverTween = transform
                        .DOPath(worldCorners, hoverDuration, PathType.CatmullRom)
                        .SetEase(Ease.Linear)
                        .SetLoops(-1, LoopType.Restart);

                    var startTime = Time.time;
                    while (true)
                    {
                        tile = gridController.GetTile(targetCoord);
                        if (tile == null || tile.Id == targetTileId)
                            break;

                        if (!easingNeedsToBeChanged && Time.time - startTime >= easingChangeTime)
                        {
                            easingNeedsToBeChanged = true;
                        }
                        
                        await UniTask.NextFrame(PlayerLoopTiming.Update, cancellationToken);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // cancellation (timeout or external) – just drop out
            }
            catch (Exception ex)
            {
                BDebug.LogError(LogCat.Match3, ex);
            }
            finally
            {
                hoverTween?.Kill();
            }
            var sourcePosition = tf.localPosition;
            var introTime = settings.IntroTime;
            Rx.Invoke(introTime*.5f, _ =>
            {
                _leftRenderer.PlayFlight();
                _rightRenderer.PlayFlight();
            });
            var multiplier = multiplierDeterminer.GetTrajectoryMultiplier(propellerCoord.X, otherCoord.X);
            
            var perpPercent = settings.PerpPercent;
            perpPercent = multiplier switch
            {
                0 => Random.Range(-perpPercent, perpPercent),
                -1 => Random.Range(-perpPercent, 0f),
                1 => Random.Range(0f, perpPercent),
                _ => perpPercent
            };
            
            var distance = propellerCoord.DistanceFrom(targetCoord.ToUnityVector2());
            var duration = settings.GetFlightDuration(distance);
            
            Rx.Invoke(duration, _ =>
            {
                foreach (var someTf in AllTransforms)
                {
                    if (_transformToPool.TryGetValue(someTf, out IPoolItem poolItem))
                        poolItem.OnRelease(); 
                    else
                        someTf.gameObject.SetActive(false);
                }
            });
            var path = PathFactory.GetCircPath(sourcePosition, targetLocalPosition, perpPercent);
            var globalMovementTweener = tf.DOLocalPath(path, duration, PathType.CatmullRom)
                .OnComplete(CompleteAction);
            
            if (easingNeedsToBeChanged)
            {
                globalMovementTweener.SetEase(Ease.Linear);
            }
            else
            {
                globalMovementTweener.SetEase(settings.Ease);
            }
            
            _tweens.Add(globalMovementTweener);
        }

        private void CompleteAction()
        {
            gameObject.Release();

        }

        public override void OnInstantiate()
        {
            base.OnInstantiate();
            foreach (var someTf in AllTransforms)
            {
                IPoolItem poolItem = someTf.GetComponent<IPoolItem>();
                if (poolItem != null)
                {
                    poolItem.OnInstantiate();
                    _transformToPool[someTf] = poolItem;
                }
                else
                    someTf.gameObject.SetActive(false);
            }
        }

        public override void OnRelease()
        {     
            if (this == null) return;

            _leftRenderer.SortingLayer = SortingLayer.NameToID(LayerNameDefault); 
            _rightRenderer.SortingLayer = SortingLayer.NameToID(LayerNameDefault);
            _leftRenderer = null;
            _rightRenderer = null;

            _animator.PlayMain();
            _propellerLayerRenderer.ResetSpine();
            
            _propellerTransform.localPosition = Vector3.zero;
            _bombTransform.localPosition = Vector3.zero;
            _rowBreakerTransform.localPosition = Vector3.zero;
            _columnBreakerTransform.localPosition = Vector3.zero;

            foreach (var tf in _transformsToRelease)
            {
                if (_transformToPool.TryGetValue(tf, out IPoolItem poolItem))
                    poolItem.OnRelease();
                else
                    tf.gameObject.SetActive(false);
            }

            _leftHolder.localPosition = Vector3.zero;
            _rightHolder.localPosition = Vector3.zero;

            _leftHolder.localScale = Vector3.one;
            _rightHolder.localScale = Vector3.one;

            _leftHolder.localRotation = Quaternion.identity;
            _rightHolder.localRotation = Quaternion.identity;

            foreach (var someTf in AllTransforms)
            {
                if (_transformToPool.TryGetValue(someTf, out IPoolItem poolItem))
                    poolItem.OnRelease();
                else
                    someTf.gameObject.SetActive(false);
            }
            
            foreach(var tween in _tweens)
                tween.Kill();

            _tweens.Clear();

            if(_coroutine != null)
                StopCoroutine(_coroutine);

            _coroutine = null;

            _callback.SafeInvoke();
            _callback = null;

            base.OnRelease();

            _sortLeftCloser = false;
        }
    }
}