using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using BBB.Match3.Renderer;
using BBB.Core;
using GameAssets.Scripts.Utils;

namespace BBB
{
    public class BombEffect : ActivePoolItem
    {
        [SerializeField]
        private float speed = 0.05f;

        [SerializeField] private int _shockWaveLimit = 10;

        [SerializeField] private List<ParticleSystem> _particleSystemsToColor;

        private Grid _grid;
        private TileController _tileController;
        private bool _finished;
        private Coords _origin;
        private int _radius;

        private Transform _selfTransform;
        private Vector3 _initialScale;

        public void Init(Grid grid, TileController tileController, Coords coords, int radius, Color color)
        {
            _grid = grid;
            _tileController = tileController;
            _origin = coords;
            _radius = radius + 1;
            var scaleMult = radius == 1 ? 1f : (radius * 2 + 1) / 5f;
            
            if (_selfTransform == null)
                _selfTransform = transform;
            
            var localScale = _selfTransform.localScale;
            _initialScale = localScale;
            localScale *= scaleMult;
            _selfTransform.localScale = localScale;
            
            foreach (var ps in _particleSystemsToColor)
            {
                var mm = ps.main;
                mm.startColor = new ParticleSystem.MinMaxGradient(color);
            }

            if (gameObject.activeInHierarchy)
            {
                StartCoroutine(RunShockwave());
            }
        }

        public override void OnRelease()
        {
            _selfTransform.localScale = _initialScale;
            base.OnRelease();
            _finished = false;
            StopAllCoroutines();
        }

        private void OnFinishedDestroyAnim()
        {
            gameObject.Release();
        }

        private IEnumerator RunShockwave()
        {
            while (!_finished)
            {
                var affected = GetAffectedTiles(_origin, _radius++);
                foreach (var coord in affected)
                {
                    var cell = _grid.GetCell(coord);
                    if (cell != null && !ReferenceEquals(cell.Tile, null))
                    {
                        var view = _tileController.GetOrCreateTileView(cell.Tile);
                        var direction = CardinalDirectionsHelper.GetCardinalDirectionFromCoords(_origin, coord);
                        view.Animator.StartShockwaveEffect(direction);
                    }
                }
                if (_radius >= _shockWaveLimit)
                {
                    _finished = true;
                    OnFinishedDestroyAnim();
                }

                yield return WaitCache.Seconds(speed);
            }
        }

        private List<Coords> GetAffectedTiles(Coords origin, int radius)
        {
            // HashSet<Coords> coords = origin.GetCoordsAround(grid, radius);
            List<Coords> coords = new List<Coords>();

            int minX = origin.X - radius;
            int minY = origin.Y - radius;
            int maxX = origin.X + radius;
            int maxY = origin.Y + radius;

            for (int i = minY; i <= maxY; i++)
            {
                AppendCoordsIfPossible(minX, i, coords);
                AppendCoordsIfPossible(maxX, i, coords);
            }

            for (int i = minX + 1; i < maxX; i++)
            {
                AppendCoordsIfPossible(i, maxY, coords);
                AppendCoordsIfPossible(i, minY, coords);
            }
            return coords;
        }

        private void AppendCoordsIfPossible(int x, int y, List<Coords> coords)
        {
            var cell = _grid.GetCell(new Coords(x, y));
            if (cell != null)
            {
                coords.Add(cell.Coords);
            }
        }
    }
}
