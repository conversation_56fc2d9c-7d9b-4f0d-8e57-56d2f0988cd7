namespace BBB.Match3.Logic
{
    public static class RefreshShelf
    {
        public static void RefreshShelfOnGrid(Grid grid, Cell c)
        {
            var sizeX = c.Tile.GetParam(TileParamEnum.SizeX);
            var sizeY = c.Tile.GetParam(TileParamEnum.SizeY);

            for (var x = c.Coords.X; x < c.Coords.X + sizeX; x++)
                for (var y = c.Coords.Y; y > c.Coords.Y - sizeY; y--)
                {
                    if (!grid.TryGetCell(new Coords(x, y), out var affectedCell)) continue;
                    affectedCell.AddMultisizeCellReference(c);
                    c.AddReferencedCell(affectedCell);
                }
        }
    }
}
