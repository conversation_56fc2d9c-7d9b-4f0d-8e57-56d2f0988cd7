using BBB;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Logic
{
    [RequireComponent(typeof(Animation))]
    public class AnimationRunner : BbbMonoBehaviour
    {
        private Animation _animation;
        
        private void Awake()
        {
            _animation = GetComponent<Animation>();
            if (_animation)
            {
                _animation.Stop();
            }
        }

        protected override void OnEnable()
        {
            base.OnEnable();
            
            if (_animation)
            {
                _animation.enabled = true;
                _animation.Play();
            }
        }

        protected override void OnDisable()
        {
            base.OnDisable();
            
            if (_animation)
            {
                _animation.Stop();
            }
        }
    }
}