using BBB;
using BBB.Match3;
using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.PopSystemTypes;

namespace GameAssets.Scripts.Match3.Logic.Tiles
{
    public sealed class BowlingTile : Tile
    {
        private const int BowlingTileClosedHp = 1;
        private static readonly (long, int) ClosedAssistState = ((long) GoalType.BowlingPin, BowlingTileClosedHp);
        private (long, int value) _assistState = ((long) GoalType.BowlingPin, DefaultHp);
        private const string BowlingCurtain = "BowlingCurtain";
        private const string Bowling = "Bowling";
        
        public BowlingTile(int id, TileAsset tileAsset, TileOrigin tileOrigin, TileKinds tileKind, List<TileParam> tileParams)
            : base(id, tileAsset, tileOrigin, tileKind, tileParams)
        {
            Speciality = TileSpeciality.Bowling;
            AllowedDamageSource = DamageSource.AllBase | DamageSource.AdjacentGeneral;
            State |= TileState.Bowling;
            AddMandatoryParamsTile();
        }

        public override IEnumerable<(long key, int value)> GetAssistState()
        {
            if (GetParam(TileParamEnum.BowlingOpened) == 0)
            {
                yield return ClosedAssistState;
            }

            _assistState.value = GetParam(TileParamEnum.AdjacentHp);
            yield return _assistState;
        }
        
        public override bool HasDieReaction()
        {
            return false;
        }

        public override bool TryApplyAdjacentDamage(SimulationContext simulationContext, HitContext hitContext)
        {
            var bowlingPinClosed = GetParam(TileParamEnum.BowlingOpened) == 0;
            if (bowlingPinClosed)
            {
                SetParam(TileParamEnum.BowlingOpened, 1);
                simulationContext.Handler.AddAction(new ActionChangeTileParam(Id, hitContext.Cell.Coords,
                    new List<(TileParamEnum, int)> {new(TileParamEnum.BowlingOpened, 1)}, hitContext.HitWaitParams));
                return true;
            }

            return base.TryApplyAdjacentDamage(simulationContext, hitContext);
        }

        public override IEnumerable<string> GetGeneralizedLayers()
        {
            if (GetParam(TileParamEnum.BowlingOpened) == 0)
            {
                yield return BowlingCurtain;
            }
            
            yield return Bowling;
        }
    }
}