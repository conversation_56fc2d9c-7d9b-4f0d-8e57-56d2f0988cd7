using System;
using System.Collections.Generic;
using BBB.Match3;
using BBB.Match3.Logic;

namespace BBB
{
    public partial class Tile
    {
        public override string ToString()
        {
            return "[" + Kind.ToString()[0] + Id + "<" + State + "> {" + Speciality +"} ]";
        }

        public string ToConsole()
        {
            return ToColorString();
        }

        public string ToStringWithOrigin()
        {
            return ToColorString() + " " + Origin.ToConsole();
        }

        public string ToColorString(int count = 0, bool inColor = true)
        {
            var digitCountInId = (int)Math.Floor((Id == 0 ? 0 : Math.Log10(Id)) + 1);
            var delta = count - digitCountInId;
            var zeroString = new string('0', delta < 0 ? 0 : delta);

            var idStr = inColor
                ? "<color=" + Kind.ToString().ToLower() + ">" + zeroString + Id + " " + Speciality + " state=" + State + "</color>"
                : zeroString + Id + " state=" + State;

            return inColor && (AllowedDamageSource & DamageSource.AdjacentGeneral) == 0
                ? "<color=black>[" + idStr + "]</color>"
                : "[" + idStr + "]";
        }

        public TileDto ToDto()
        {
            var dto = new TileDto
            {
                Id = Id,
                Asset = Asset,
                Kind = (int)Kind,
                State = (ulong)State,
                TileParams = GetParamsDto(),
                Speciality = Speciality,
                hp = _hp
            };
            return dto;
        }

        public void FromDto(TileDto dto)
        {
            Id = dto.Id;
            Asset = dto.Asset;
            Kind = (TileKinds)dto.Kind;
            State = (TileState)dto.State;
            _params = dto.TileParams == null ? null : new List<TileParam>(dto.TileParams);
            Speciality = dto.Speciality;
            AllowedDamageSource = TileMapper.GetTileSpecialityAllowedDamageSource(dto.Speciality);
            BoostersApplicability = TileMapper.GetAssetDefaultBoosterApplicability(dto.Speciality);
            _hp = dto.hp;
        }
    }
}