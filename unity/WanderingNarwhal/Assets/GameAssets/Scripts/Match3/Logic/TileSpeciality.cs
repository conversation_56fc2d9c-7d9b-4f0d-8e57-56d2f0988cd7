namespace BBB
{
    //do not change the numbers
    public enum TileSpeciality
    {
        None            = 0,
        Sticker         = 1,
        BlinkingTile    = 2, // Tile left after the Bomb explode
        RowBreaker      = 3, // Clears row when destroyed
        ColumnBreaker   = 4, // Clears column when destroyed
        Bomb            = 5, // Clears block when destroyed
        ColorBomb       = 6, // Will clear all the tiles of a given match speciality when swapped.
        DropItem        = 7,
        Litter          = 8,
        <PERSON>nat<PERSON>          = 9,
        Sand            = 11,
        Frame           = 16,
        ColorCrate      = 17,
        Watermelon      = 18,
        MoneyBag        = 19,
        <PERSON>         = 20,
        <PERSON>             = 21,
        <PERSON>            = 22,
        <PERSON><PERSON>           = 23,
        <PERSON><PERSON>          = 24,
        <PERSON>          = 25,
        <PERSON>kunk           = 26,
        Hen             = 27,
        <PERSON>         = 28,
        <PERSON>ve            = 29,
        <PERSON>             = 30,
        <PERSON><PERSON>            = 31,
        <PERSON>quid           = 32,
        Toad            = 33,
        Propeller       = 34,
        <PERSON>         = 35,
        <PERSON>            = 36,
        Soda            = 37,
        MagicHat        = 38,
        Safe            = 39,
        FlowerPot       = 40,
        IceBar          = 41,
        DynamiteBox     = 42,
        GiantPinata     = 43,
        MetalBar        = 44,
        She<PERSON>           = 45,
        Je<PERSON><PERSON><PERSON>       = 46,
        <PERSON>Scarab    = 47,
        Gondola         = 48,
        <PERSON>kTuk          = 49,
        FireWorks       = 50,
        SlotMachine     = 51,
        <PERSON>Mon<PERSON>       = 52,
        <PERSON>           = 53,
        <PERSON>uch           = 54,
    }

    public static class TileSpecialityHelper
    {
        public static bool TileSpecialityComparison(TileSpeciality firstTileSpeciality,
            TileSpeciality secondTileSpeciality)
        {
            if (firstTileSpeciality is TileSpeciality.RowBreaker or TileSpeciality.ColumnBreaker
                && secondTileSpeciality is TileSpeciality.RowBreaker or TileSpeciality.ColumnBreaker)
            {
                return true;
            }

            if (firstTileSpeciality == TileSpeciality.ColorBomb)
            {
                return true;
            }

            if (secondTileSpeciality == TileSpeciality.ColorBomb)
            {
                return false;
            }

            return firstTileSpeciality >= secondTileSpeciality;
        }

        public static int GetSpawnPriority(this TileSpeciality spec)
        {
            return spec switch
            {
                TileSpeciality.ColorBomb => 10,
                TileSpeciality.Propeller => 9,
                TileSpeciality.Bomb => 8,
                TileSpeciality.RowBreaker => 7,
                TileSpeciality.ColumnBreaker => 6,
                _ => 0
            };
        }
        
        public static bool IsRegularBoost(this TileSpeciality spec)
        {
            return spec is TileSpeciality.RowBreaker or TileSpeciality.ColumnBreaker or
                TileSpeciality.Bomb or TileSpeciality.Propeller;
        }

        public static bool IsBoost(this TileSpeciality spec)
        {
            return spec is TileSpeciality.RowBreaker or TileSpeciality.ColumnBreaker or
                TileSpeciality.Bomb or TileSpeciality.ColorBomb or TileSpeciality.Propeller;
        }
    }
}
