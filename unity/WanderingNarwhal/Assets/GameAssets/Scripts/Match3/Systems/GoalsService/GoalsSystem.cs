using System;
using System.Collections.Generic;
using System.Text;
using BBB.CellTypes;
using BBB.DI;
using BBB.Match3.Systems.CreateSimulationSystems;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Messages;

namespace BBB.Match3.Systems.GoalsService
{

    public class EndTurnEvent : Message<GoalState>
    {
    }

    /// <summary>
    /// Always use event dispatcher for this class communication to MonoBehaviours and other engine classes
    /// Public methods of this class may be called from m3 simulation on a secondary thread
    /// </summary>
    public sealed class GoalsSystem : IContextInitializable
    {
        private int _comboCount;
        private GoalState _goalsProgressLeft;
        private GoalState _originalGoals;
        private GoalState _dynamicGoals;

        private readonly List<GoalType> _cachedGoals = new (3);

        private readonly List<int> _tilesSkipDestroyAnim = new (10);
        private bool _levelWon;
        private ILevel _level;

        private IEventDispatcher _eventDispatcher;
        private GameEventMatch3ManagersCollection _managersCollection;
        private IRaceEventMatch3Manager _raceEventMatch3Manager;
        private IContext _context;

        private Dictionary<Coords, Path> _cachedPath = new ();

        /// <summary>
        /// Non limited score, total number that potentially continues to increase after the goal score reached
        /// </summary>
        public int AccumulativeNonLimitedScore { get; private set; }

        public GoalState ProgressAchieved => _originalGoals - _goalsProgressLeft;

        public GoalState OriginalGoals => _originalGoals;

        public GoalState GoalProgressLeft => _goalsProgressLeft;

        public IEnumerable<GoalType> OriginalGoalTypes
        {
            get
            {
                foreach (var goal in _originalGoals)
                {
                    yield return goal.Key;
                }
            }
        }

        public IRaceEventMatch3Manager RaceEventMatch3Manager => _raceEventMatch3Manager;

        public void InitializeByContext(IContext context)
        {
            _cachedGoals.Clear();
            _tilesSkipDestroyAnim.Clear();
            _context = context;

            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _raceEventMatch3Manager = context.Resolve<IRaceEventMatch3Manager>();
            _managersCollection = context.Resolve<GameEventMatch3ManagersCollection>();
        }

        public void ResetDefaults()
        {
            _comboCount = default;
            _goalsProgressLeft = default;
            _originalGoals = default;
            _dynamicGoals = default;
        }

        public void ResetOnRetry(ILevel level)
        {
            RefreshForLevel(level);
        }

        public int NumberOfOverAllGoalsLeft()
        {
            var numberOfAllGoals = 0;
            foreach (var goalType in GoalTypeExtensions.GetAllSingularGoals())
            {
                var original = _originalGoals.GetGoalValue(goalType);

                if (original == 0)
                    continue;

                numberOfAllGoals += _goalsProgressLeft.GetGoalValue(goalType);
            }

            return numberOfAllGoals;
        }

        public Dictionary<GoalType, float> GetGoalProgressValues()
        {
            var result = new Dictionary<GoalType, float>();

            foreach (GoalType goalType in GoalTypeExtensions.GetAllSingularGoals())
            {
                var original = _originalGoals.GetGoalValue(goalType);

                var dynamic = _dynamicGoals.GetGoalValue(goalType);
                
                original += dynamic;

                if (original == 0)
                    continue;

                var left = _goalsProgressLeft.GetGoalValue(goalType);

                var ratio = (float)left / original;
                result.Add(goalType, 1f - ratio);
            }

            return result;
        }

        public int GetAchievedGoalCount(GoalType goalType)
        {
            var original = _originalGoals.GetGoalValue(goalType);
            var left = _goalsProgressLeft.GetGoalValue(goalType);
            return original - left;
        }

        public int GetOriginalGoalCount(GoalType goalType)
        {
            return _originalGoals.GetGoalValue(goalType);
        }

        public int GetLeftGoalCount(GoalType goalType)
        {
            return _goalsProgressLeft.GetGoalValue(goalType);
        }

        public int GetTilesLeftToFly(TileKinds tileKind)
        {
            if (AreBeingCollectedByGameEvent(tileKind))
                return int.MaxValue;

            return GetLeftGoalCount(tileKind.ToGoalType());
        }

        public bool AreBeingCollectedByGameEvent(TileKinds tileKind)
        {
            if (_managersCollection == null) return false;
            return _managersCollection.IsAnyManager(manager =>
            {
                if (manager.ActiveGameEvent is not {Status: GameEventStatus.Active})
                    return false;

                var gamePlayType = manager.GetGameplayType();

                if (gamePlayType != GameEventGameplayType.Collection)
                    return false;

                if (!(manager is DailyCollectGameEventMatch3Manager collectEventManager))
                    return false;

                var milestoneTarget = collectEventManager.MilestoneTarget;

                return milestoneTarget.IsGoalColorTile() && tileKind == milestoneTarget.ToTileKind();
            });
        }

        public bool IsGoalExist(GoalType goalType)
        {
            return _goalsProgressLeft.DoesGoalExist(goalType);
        }

        public void RemoveRemainingGoalsExceptScore()
        {
            _goalsProgressLeft.RemoveGoalsExceptScore();
        }

        public GoalType ReduceGoalOnCellBackgroundDamageIfNeeded(Cell cell)
        {
            if (cell.IvyCount == 1)
            {
                if (cell.IsAnyOf(CellState.Ivy) && _goalsProgressLeft.DoesGoalExist(GoalType.Ivy))
                {
                    if (TryReduceGoalIfNeeded(GoalType.Ivy))
                    {
                        return GoalType.Ivy;
                    }
                }

                return GoalType.None;
            }

            if (cell.BackgroundCount != 1) return GoalType.None;
            if (cell.IsAnyOf(CellState.BackOne) && _goalsProgressLeft.DoesGoalExist(GoalType.Backgrounds))
            {
                if (TryReduceGoalIfNeeded(GoalType.Backgrounds))
                {
                    return GoalType.Backgrounds;
                }
            }
            else if (cell.IsAnyOf(CellState.Petal) && _goalsProgressLeft.DoesGoalExist(GoalType.Petal))
            {
                if (TryReduceGoalIfNeeded(GoalType.Petal))
                {
                    return GoalType.Petal;
                }
            }

            return GoalType.None;
        }

        public void ReduceGoalsOnTileDestroyIfNeeded(TileSpeciality tileSpeciality, TileState tileState,
            TileKinds tileKind, ref List<GoalTypeTagPair> affectedGoals)
        {
            // Reduce goal if tile is related to one or more goals and target goal is not automatic.
            var nonZeroGoals = _cachedGoals;
            nonZeroGoals.Clear();
            foreach (var kvp in _goalsProgressLeft)
            {
                if (kvp.Value <= 0) continue;
                nonZeroGoals.Add(kvp.Key);
            }

            foreach (var goal in nonZeroGoals)
            {
                if (!GoalState.IsTileGridBasedGoalRelatedItem(tileSpeciality, tileState, tileKind, goal)) continue;

                if (TryReduceGoalIfNeeded(goal))
                {
                    affectedGoals.Add(new GoalTypeTagPair(goal));
                }
            }

            if (_managersCollection == null) return;
            foreach (var manager in _managersCollection)
            {
                if (manager.ActiveGameEvent is not {Status: GameEventStatus.Active}) continue;

                if (manager.IsNormalTileReskinGameEventType)
                {
                    if (tileSpeciality != TileSpeciality.None ||
                        tileKind != manager.NormalTileSelectedForReskinOnThisLevel)
                        continue;
                    affectedGoals.Add(new GoalTypeTagPair(GoalType.GameEventScore, manager.ActiveGameEventUid));
                    manager.AddGameEventScore(1);
                }
                else
                {
                    var gameEventType = manager.GetGameplayType();
                    if (gameEventType == GameEventGameplayType.Competition)
                    {
                        if (!tileSpeciality.IsBoost()) continue;
                        affectedGoals.Add(new GoalTypeTagPair(GoalType.GameEventScore, manager.ActiveGameEventUid));
                        manager.AddGameEventScore(1);
                    }
                    else if (gameEventType == GameEventGameplayType.Collection)
                    {
                        var collectEventManager = manager as DailyCollectGameEventMatch3Manager;
                        var milestoneTarget = collectEventManager?.MilestoneTarget ?? default;

                        if (tileSpeciality == TileSpeciality.None && milestoneTarget.IsGoalColorTile() &&
                            tileKind == milestoneTarget.ToTileKind())
                        {
                            affectedGoals.Add(new GoalTypeTagPair(GoalType.GameEventScore, manager.ActiveGameEventUid));
                            manager.AddGameEventScore(1);
                        }
                        else if (milestoneTarget.IsGoalPowerUp() &&
                                 milestoneTarget == CollectionGameEvent.FromTileSpeciality(tileSpeciality))
                        {
                            affectedGoals.Add(new GoalTypeTagPair(GoalType.GameEventScore, manager.ActiveGameEventUid));
                            manager.AddGameEventScore(1);
                        }
                    }
                }
            }
        }

        public GoalType ReduceGoalOnModRemoveIfNeeded(TileState mod)
        {
            if (mod == TileState.None) return GoalType.None;

            var nonZeroGoals = _cachedGoals;
            nonZeroGoals.Clear();
            foreach (var kvp in _goalsProgressLeft)
            {
                if (kvp.Value <= 0) continue;
                var goal = kvp.Key;
                nonZeroGoals.Add(goal);
            }

            foreach (var goal in nonZeroGoals)
            {
                if (!GoalState.IsModStateRelatedToGoal(mod, goal))
                    continue;

                if (TryReduceGoalIfNeeded(goal))
                {
                    foreach (var manager in _managersCollection)
                    {
                        if (manager != null)
                        {
                            if (manager.ActiveGameEvent is not {Status: GameEventStatus.Active}) continue;

                            var gameEventType = manager.GetGameplayType();
                            switch (gameEventType)
                            {
                                case GameEventGameplayType.Collection:
                                    if (GoalState.IsModStateRelatedToGoal(mod, GoalType.GameEventScore))
                                    {
                                        manager.AddGameEventScore(1);
                                    }

                                    break;
                            }
                        }
                    }

                    return goal;
                }
            }

            return GoalType.None;
        }

        /// <summary>
        /// Decrement target goal value if it exists and it is positive.
        /// </summary>
        public bool TryReduceGoalIfNeeded(GoalType goal)
        {
            if (_goalsProgressLeft.DoesGoalExist(goal))
            {
                _goalsProgressLeft.Reduce(goal, 1);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Increase goal value if it exist.
        /// </summary>
        /// <remarks>
        /// Currently only one goal type can
        /// increase its value - Sand
        /// which can grow on grid once per turn.
        /// </remarks>
        public bool TryIncreaseGoalIfNeeded(GoalType goal)
        {
            if (_goalsProgressLeft.DoesGoalExist(goal))
            {
                _goalsProgressLeft.Reduce(goal, -1);
                return true;
            }

            return false;
        }

        public void TryAddGoalIfNeeded(GoalType goal)
        {
            if (!_goalsProgressLeft.DoesGoalExist(goal))
            {
                _goalsProgressLeft.AddNewGoal(goal, 0);
                _dynamicGoals ??= new GoalState();
                _dynamicGoals.AddNewGoal(goal, 0);
            }

            _goalsProgressLeft.Reduce(goal, -1);
            _dynamicGoals.Reduce(goal, -1);
        }

        public int GetExistingGoalCount(Grid grid, GoalType goalType)
        {
            return _goalsProgressLeft.CountCellsRelatedToGoal(grid, goalType);
        }

        public GoalsSystem Clone()
        {
            var clone = new GoalsSystem();
            clone._comboCount = _comboCount;
            clone._goalsProgressLeft = _goalsProgressLeft.Clone();
            clone._originalGoals = _originalGoals.Clone();
            clone.InitializeByContext(_context);
            clone.RefreshForLevel(_level);
            return clone;
        }

        public bool HasCompletedGoals()
        {
            return !_goalsProgressLeft.AnyGoalsLeft();
        }

        public string RemainingGoalsString => _goalsProgressLeft.ToString();

        public void RefreshForLevel(ILevel level)
        {
            //_optionalGoalsEnabled = level.OptionalGoalsEnabled;
            _level = level;
            _originalGoals = level.Goals;
            _goalsProgressLeft = _originalGoals.Clone();
            AccumulativeNonLimitedScore = 0;
            _levelWon = false;
            
            if (_dynamicGoals == null)
            {
                _dynamicGoals = new GoalState();
            }
            else
            {
                _dynamicGoals.RemoveAllGoals();
            }
        }

        public void StartNewTurn()
        {
            _comboCount = -1;
            _tilesSkipDestroyAnim.Clear();
        }

        public void AddTileSkipDestroyAnim(int tileId)
        {
            _tilesSkipDestroyAnim.Add(tileId);
        }

        public bool IsTileShouldSkipDestroyAnim(int tileId)
        {
            return _tilesSkipDestroyAnim.Contains(tileId);
        }

        public void OnEndTurn()
        {
            if (_eventDispatcher == null) return;
            
            var endTurnEvent = _eventDispatcher.GetMessage<EndTurnEvent>();
            endTurnEvent.Set(_goalsProgressLeft);
            _eventDispatcher.TriggerEventNextFrame(endTurnEvent);
        }


        public void ResetGoalsProgressLeft()
        {
            _goalsProgressLeft = _originalGoals.Clone();
        }

        public override string ToString()
        {
            return new StringBuilder(string.Empty)
                .Append(" OriginalGoals: ")
                .Append(_originalGoals)
                .Append(" GoalsLeft: ")
                .Append(_goalsProgressLeft)
                .ToString();
        }

        public GoalsSystemDto ToDto()
        {
            var dto = new GoalsSystemDto
            {
                goalsProgressLeft = _goalsProgressLeft?.ToDto(),
                originalGoals = _originalGoals?.ToDto()
            };

            return dto;
        }

        public void FromDto(GoalsSystemDto dto)
        {
            if (dto.goalsProgressLeft == null)
            {
                _goalsProgressLeft = null;
            }
            else
            {
                _goalsProgressLeft ??= new GoalState();
                _goalsProgressLeft.FromDto(dto.goalsProgressLeft);
            }

            if (dto.originalGoals == null)
            {
                _originalGoals = null;
            }
            else
            {
                _originalGoals ??= new GoalState();
                _originalGoals.FromDto(dto.originalGoals);
            }
        }

        public void OnLevelWin()
        {
            _levelWon = true;
            //BfgFunnelEvent.LevelWin(_level.LevelUid);
        }

        /// <summary>
        /// Returns cell coords with goals or path coords for drop item goal
        /// </summary>
        public void CalculateGoalRelatedCoords(Grid grid, ref HashSet<Coords> goalCoords)
        {
            goalCoords.Clear();
            Span<bool> tntKinds = stackalloc bool[8]; //None(0) - White(7)
            tntKinds.Clear();

            Span<bool> tuktukKinds = stackalloc bool[8]; //None(0) - White(7)
            tuktukKinds.Clear();

            if (_goalsProgressLeft.DoesGoalExist(GoalType.Tnt) || _goalsProgressLeft.DoesGoalExist(GoalType.TukTuk))
            {
                foreach (var cell in grid.Cells)
                {
                    if (cell.IsAnyOf(CellState.Tnt) && cell.TntCount > 0)
                        tntKinds[(int)cell.TntKind] = true;

                    var mainCell = cell.GetMainCellReference(out _);
                    if (mainCell.HasTile() && mainCell.Tile.Speciality == TileSpeciality.TukTuk)
                    {
                        tuktukKinds[mainCell.Tile.GetParam(TileParamEnum.TukTukColor)] = true;
                    }
                }
            }

            foreach (var cell in grid.Cells)
            {
                foreach (var goalState in _goalsProgressLeft)
                {
                    if (GoalState.IsCellContainsGridBasedGoalRelatedItem(cell, goalState.Key)
                        || goalState.Key == GoalType.Animal && cell.Tile?.Speciality == TileSpeciality.Frame
                        || goalState.Key == GoalType.Tnt && cell.Tile != null && cell.Tile.Kind > 0 &&
                        tntKinds[(int)cell.Tile.Kind]
                        || goalState.Key == GoalType.TukTuk && cell.HasTile() &&
                        cell.Tile.Speciality == TileSpeciality.TukTuk && tuktukKinds[(int)cell.Tile.Kind])
                    {
                        switch (goalState.Key)
                        {
                            case GoalType.DropItems:
                                //add all non blocked cells on the path for drop item
                                RoutingCellSystem.CollectCoordsInCellPath(grid, cell, goalCoords, ref _cachedPath);
                                break;
                            default:
                                goalCoords.Add(cell.Coords);
                                foreach (var referencedCell in cell.ReferencedCells)
                                {
                                    goalCoords.Add(referencedCell.Coords);
                                }
                                break;
                        }
                    }
                }

                if (cell.HasTile() && cell.Tile.IsAnyOf(TileState.SkunkMod))
                {
                    goalCoords.Add(cell.Coords);
                }
            }
        }
    }
}