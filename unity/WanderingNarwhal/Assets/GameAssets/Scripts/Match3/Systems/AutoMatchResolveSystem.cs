using System.Collections.Generic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.AssistSystemTypes;

namespace BBB.Match3.Systems
{
    public static class AutoMatchResolveSystem
    {
        private static TileKindsBag Bag = new TileKindsBag(); 
        
        public static bool Resolve(Grid grid, List<TileKinds> usedKinds)
        {
            var matches = SearchMatchesSystem.FindAllMatches(grid);
            
            if (matches == null)
                return true;
            
            Bag.SetupUsed(usedKinds);

            bool result = true;
            foreach (var match in matches)
            {
                result &= Resolve(match, grid);
            }

            return result && SearchMatchesSystem.AreNoMatches(grid);
        }
        
        private static Cell[] CellRbltNeighbours = new Cell[4];

        private static bool Resolve(Match match, Grid grid)
        {
            var matchKind = match.Kind;
            foreach (var coord in match.GetAllCoordsStartingInTheMiddle())
            {
                Bag.Remove(matchKind);
                grid.TryGetCell(coord, out var cell);
                var tile = cell.Tile;

                var dir = new Coords(1,0);
                for (int i = 0; i < 4; i++)
                {
                    var firstNeighbourCoord = coord + dir;
                    var secondNeighbourCoord = coord + 2 * dir;

                    grid.TryGetCell(firstNeighbourCoord, out var firstCell);
                    grid.TryGetCell(secondNeighbourCoord, out var secondCell);

                    if(TwoMatching(firstCell, secondCell))
                        Bag.Remove(firstCell.Tile.Kind);

                    CellRbltNeighbours[i] = firstCell;
                    
                    dir = dir.Perp();
                }

                if(TwoMatching(CellRbltNeighbours[0], CellRbltNeighbours[2]))
                    Bag.Remove(CellRbltNeighbours[0].Tile.Kind);
                
                if(TwoMatching(CellRbltNeighbours[1], CellRbltNeighbours[3]))
                    Bag.Remove(CellRbltNeighbours[1].Tile.Kind);
                
                for (int i = 0; i < CellRbltNeighbours.Length; i++)
                    CellRbltNeighbours[i] = null;
                
                if(!Bag.IsEmpty())
                {
                    tile.SetKind(Bag.GetRandomKind());

                    if (IsResolved(match, grid))
                    {
                        return true;
                    }
                }
                
                Bag.Reset();
            }

            return false;
        }

        private static bool IsResolved(Match match, Grid grid)
        {
            int matchKindInARow = 0;
            foreach (var coord in match.GetAllCoords())
            {
                grid.TryGetCell(coord, out var cell);
                var tile = cell.Tile;

                if (tile.Kind == match.Kind)
                {
                    matchKindInARow++;

                    if (matchKindInARow >= 3)
                        return false;
                }
                else
                {
                    matchKindInARow = 0;
                }
            }

            return true;
        }


        private static bool TwoMatching(Cell firstCell, Cell secondCell)
        {
            return firstCell != null && firstCell.IsBaseCellMatchable() && !ReferenceEquals(firstCell.Tile, null) &&
                   firstCell.Tile.IsMatchable()
                   && secondCell != null && secondCell.IsBaseCellMatchable() && !ReferenceEquals(secondCell.Tile, null) &&
                   secondCell.Tile.IsMatchable()
                   && firstCell.Tile.Kind == secondCell.Tile.Kind;
        }
    }
}