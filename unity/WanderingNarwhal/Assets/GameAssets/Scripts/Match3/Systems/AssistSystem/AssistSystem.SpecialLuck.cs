using System;
using System.Collections.Generic;
using BBB.Match3.Debug;
using BBB.Match3.Systems.CreateSimulationSystems.AssistSystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.Match3.Systems.GoalsService;
using FBConfig;
using UnityEngine;

namespace BBB.Match3.Systems.CreateSimulationSystems
{
    public partial class AssistSystem
    {
        private struct ScoredGrid
        {
            public Grid Grid;
            public float Score;
        }

        private struct GenerateGridInfo
        {
            public Grid Grid;
            public int PossiblePowerUpCount;
            public int UsefulPowerUpCount;
            public int PossibleBombCount;
            public int UsefulBombCount;
            public int PossiblePropellerCount;
            public int PossibleLineBreakerCount;
            public int UsefulLineBreakerCount;
            public int PossibleDiscoBallCount;
            public int UsefulDiscoBallCount;
            public int UsefulMoves;
            public int EstimatedMoveDamage;

            public override string ToString()
            {
                return $"[GRID: PPU={PossiblePowerUpCount} PBMB={PossibleBombCount} PPLLR={PossiblePropellerCount} PLB={PossibleLineBreakerCount} PBLT={PossibleDiscoBallCount} UM={UsefulMoves} EDMG={EstimatedMoveDamage}]";
            }
        }

        private const int SimulationsToRunCap = 40;
        
        private readonly List<GenerateGridInfo> _allGrids = new (SimulationsToRunCap);
        private HashSet<Coords> _affectedGoalCoords = new();
        private HashSet<Coords> _goalCoords = new ();
        private Dictionary<Coords, Tile> _tilesToRestore = new ();
        private HashSet<PossibleMove> _possibleMoves = new ();

        private static int _gridHelpCoolDown;
        private static int _autoMatchesCoolDown;

        private static readonly Comparer<GenerateGridInfo> Comparer = Comparer<GenerateGridInfo>.Create((info, info1) =>
        {
            var formula = FX(info);
            var formula1 = FX(info1);

            return formula.CompareTo(formula1);

            float FX(GenerateGridInfo gridInfo) => (gridInfo.PossibleDiscoBallCount + gridInfo.UsefulDiscoBallCount) * 5000f + 
                                               (gridInfo.PossibleBombCount + gridInfo.UsefulBombCount) * 2500f + 
                                               (gridInfo.PossibleLineBreakerCount + gridInfo.UsefulLineBreakerCount) * 1000f + 
                                               gridInfo.PossiblePropellerCount * 1000f + gridInfo.UsefulMoves;
        });

        private void DefineByAssistParams(Grid grid, IRootSimulationHandler simHandler, SimulationInputParams inputParams,
            GoalsSystem goalsSystem, AssistParams assistParams, int iteration)
        {
            var selectedGrid = GetGridByAssistParams(grid, simHandler, inputParams, goalsSystem, assistParams, iteration);
            CopyDefinitions(selectedGrid, grid, simHandler);
        }

        /// <summary>
        /// Runs simulation for several grids, gathers statistics (like # of useful moves, # of possible boosters, etc)
        /// about those grids and picks one based on assist params win/loss value
        /// </summary>
        /// <param name="grid"></param>
        /// <param name="simHandler"></param>
        /// <param name="inputParams"></param>
        /// <param name="goalsSystem"></param>
        /// <param name="assistParams"></param>
        /// <param name="goalCoords"></param>
        /// <param name="iteration"></param>
        /// <returns></returns>
        private Grid GetGridByAssistParams(Grid grid, IRootSimulationHandler simHandler,
            SimulationInputParams inputParams, GoalsSystem goalsSystem, AssistParams assistParams, int iteration)
        {
            simHandler.AddAction(new ActionAssistLog(assistParams.ToString()));

            _allGrids.Clear();
            
            var modifiedGrid = grid.DefinitionAttemptClone();
            //apply auto matches on first iteration only
            if (iteration == 0)
            {
                AddAutoMatches(modifiedGrid, assistParams, inputParams.UsedKinds);
            }

            var mechanicTargetingManager = new MechanicTargetingManager(inputParams.Settings.MechanicTargetingConfigs);
            
            var simulationsToRun = Math.Min(assistParams.AssistSystemConfig.SimulationsToRun, SimulationsToRunCap);
            for (var i = 0; i < simulationsToRun; i++)
            {
                var generatedGrid = modifiedGrid.DefinitionAttemptClone();
                if (i == 0)
                {
                    MinimumDefine(generatedGrid, inputParams.UsedKinds);
                }
                else
                {
                    StandardDefine(generatedGrid, inputParams.UsedKinds, assistParams);
                }
                
                var isLastGrid = i == simulationsToRun - 1; 
                var filterGridWithoutMove = !isLastGrid || _allGrids.Count > 0; //we need at least one grid to select, even with reshuffling
                if (filterGridWithoutMove && !SearchMatchesSystem.SearchForAnyPossibleMove(generatedGrid, true))
                    continue;   //grid doesn't have any move and will reshuffle, exclude it from calculations
                
                _possibleMoves.Clear();
                SearchMatchesSystem.SearchForAllPossibleMovesNoAlloc(generatedGrid, ref _possibleMoves);
                //get goal coords for each generated grid, because it could have a new tile color goal coords
                goalsSystem.CalculateGoalRelatedCoords(grid, ref _goalCoords);
                mechanicTargetingManager.Init(generatedGrid, goalsSystem);

                var maxGridInfo = new GenerateGridInfo
                {
                    Grid = generatedGrid
                };
                var usefulMovesCount = 0;
                foreach (var possibleMove in _possibleMoves)
                {
                    if (HasAffectedGoalCoords(generatedGrid, possibleMove, mechanicTargetingManager,
                            _goalCoords, goalsSystem, ref _affectedGoalCoords,
                            ref _tilesToRestore))
                    {
                        usefulMovesCount++;
                        maxGridInfo.EstimatedMoveDamage = Math.Max(maxGridInfo.EstimatedMoveDamage, _affectedGoalCoords.Count);
                    }

                    maxGridInfo.UsefulMoves = Math.Max(maxGridInfo.UsefulMoves, usefulMovesCount);
                    
                    if (possibleMove.Matches == null)
                        continue;
                    var gridInfo = new GenerateGridInfo();
                    var previousMatches = new List<Match>();
                    foreach (var match in possibleMove.Matches)
                    {
                        if (match.Length > 3)
                        {
                            maxGridInfo.PossiblePowerUpCount = Math.Max(maxGridInfo.PossiblePowerUpCount, ++gridInfo.PossiblePowerUpCount);
                        }
                        if (match.Length == 4 && match.MatchType != MatchType.Square)
                        {
                            maxGridInfo.PossibleLineBreakerCount = Math.Max(maxGridInfo.PossibleLineBreakerCount, ++gridInfo.PossibleLineBreakerCount);
                            var speciality = TileSpeciality.RowBreaker;
                            if (match.GetSimplifiedDirections() == SimplifiedDirections.Vertical)
                            {
                                speciality = TileSpeciality.ColumnBreaker;
                            }
                            if (CanAffectGoalCoords(match, speciality, grid, _goalCoords))
                            {
                                maxGridInfo.UsefulLineBreakerCount = Mathf.Max(maxGridInfo.UsefulLineBreakerCount, ++gridInfo.UsefulLineBreakerCount);
                                maxGridInfo.UsefulPowerUpCount = Math.Max(maxGridInfo.UsefulPowerUpCount, ++gridInfo.UsefulPowerUpCount);
                            }
                        }
                        if (match.MatchType == MatchType.Square)
                        {
                            maxGridInfo.PossiblePropellerCount = Math.Max(maxGridInfo.PossiblePropellerCount, ++gridInfo.PossiblePropellerCount);
                            maxGridInfo.UsefulPowerUpCount = Math.Max(maxGridInfo.UsefulPowerUpCount, ++gridInfo.UsefulPowerUpCount);
                        }

                        if (match.Length >= 5)
                        {
                            maxGridInfo.PossibleDiscoBallCount = Math.Max(maxGridInfo.PossibleDiscoBallCount, ++gridInfo.PossibleDiscoBallCount);
                            if (CanAffectGoalCoords(match, TileSpeciality.ColorBomb, grid, _goalCoords))
                                maxGridInfo.UsefulPowerUpCount = Math.Max(maxGridInfo.UsefulPowerUpCount, ++gridInfo.UsefulPowerUpCount);
                        }

                        foreach (var previousMatch in previousMatches)
                        {
                            if (match.Length is < 3 or >= 5 || !match.AnyOverlap(previousMatch) || match.MatchType == MatchType.Square) continue;
                            
                            maxGridInfo.PossibleBombCount = Math.Max(maxGridInfo.PossibleBombCount, ++gridInfo.PossibleBombCount);
                            if (CanAffectGoalCoords(match, TileSpeciality.Bomb, grid, _goalCoords))
                            {
                                maxGridInfo.UsefulPowerUpCount = Math.Max(maxGridInfo.UsefulPowerUpCount, ++gridInfo.UsefulPowerUpCount);
                            }
                        }
                        
                        previousMatches.Add(match);
                    }
                }
                
                _allGrids.Add(maxGridInfo);
            }
            mechanicTargetingManager.UnInit();
            var selectedGridInfo = SelectGridByAssistParams(_allGrids, assistParams, goalsSystem);

            simHandler.AddAction(new ActionAssistLog(selectedGridInfo.ToString()));
            return selectedGridInfo.Grid;
        }

        private static void SetupAssistCooldowns(AssistParams assistParams)
        {
            SetupHelpCoolDown(assistParams);
            SetupAutoMatchesCoolDown(assistParams);
        }

        private static void SetupHelpCoolDown(AssistParams assistParams)
        {
            _gridHelpCoolDown = assistParams?.AssistSystemConfig.GridHelpCooldown ?? int.MaxValue;
        }
        
        private static void SetupAutoMatchesCoolDown(AssistParams assistParams)
        {
            if (assistParams == null)
            {
                _autoMatchesCoolDown = int.MaxValue;
                return;
            }

            _autoMatchesCoolDown = AssistParams.AimAtWinningLevel
                ? assistParams.AssistSystemConfig.AimingToWin?.AutomatchHelpCooldown ?? int.MaxValue
                : assistParams.AssistSystemConfig.AimingToLose?.AutomatchHelpCooldown ?? int.MaxValue;
        }

        public static bool IsInGridHelpCoolDown(AssistParams assistParams)
        {
            var diff = assistParams?.WinLossValueDiff();

            return diff < 0 && _gridHelpCoolDown > 0 && (
                   diff < assistParams.AssistSystemConfig.SkipGridHelpCooldownThreshold ||
                     !AssistParams.AimAtWinningLevel);
        }
        
        /// <summary>
        /// Picks a grid from the list based on the assist params win/loss values provided
        ///
        /// TODO: try to put this logic in the config
        /// </summary>
        /// <param name="grids"></param>
        /// <param name="assistParams"></param>
        /// <returns></returns>
        private static GenerateGridInfo SelectGridByAssistParams(List<GenerateGridInfo> grids, AssistParams assistParams, GoalsSystem goalsSystem)
        {
            if (grids.Count < 2) return grids[0];

            grids.Sort(Comparer);
            
            if (goalsSystem.HasCompletedGoals()) //already won, don't help anymore
                return grids[0];

            var diff = assistParams?.WinLossValueDiff();
            
            var willGetHelp = diff <= 0;
            var isInGridCooldown = _gridHelpCoolDown > 0;
            var shouldSkipGridCooldown = diff < assistParams.AssistSystemConfig.SkipGridHelpCooldownThreshold;
            if (willGetHelp && isInGridCooldown && !shouldSkipGridCooldown)
            {
                _gridHelpCoolDown--;

                // When aiming to lose, don't help at all - return the worst grid
                if (!AssistParams.AimAtWinningLevel)
                {
                    return GetGenerateGridInfo(assistParams.AssistSystemConfig.HelpLevels(0).Value, grids);
                }
            }

            if (_gridHelpCoolDown <= 0)
            {
                SetupHelpCoolDown(assistParams);
            }

            if (assistParams.LossValue > assistParams.AssistSystemConfig.UsefulMovesOnlyThreshold)
            {
                var hasUsefulGrids = false;

                foreach (var grid in grids)
                {
                    if (grid.UsefulMoves <= 0) continue;
                    
                    hasUsefulGrids = true;
                    break;
                }

                if (hasUsefulGrids)
                {
                    for (var i = grids.Count - 1; i >= 0; i--)
                    {
                        if (grids[i].UsefulMoves == 0)
                        {
                            grids.RemoveAt(i);
                        }
                    }
                }
            }
            
            for (var i = 0; i < assistParams.AssistSystemConfig.HelpLevelsLength; i++)
            {
                var helpLevel = assistParams.AssistSystemConfig.HelpLevels(i).Value;
                if (diff >= helpLevel.Threshold)
                {
                    return GetGenerateGridInfo(helpLevel, grids);
                }
            }

            return GetGenerateGridInfo(assistParams.AssistSystemConfig.HelpLevels(assistParams.AssistSystemConfig.HelpLevelsLength - 1).Value, grids);
        }

        private static GenerateGridInfo GetGenerateGridInfo(HelpLevelValues helpLevelValues, List<GenerateGridInfo> grids)
        {
            return grids[(int) ((grids.Count - 1) * helpLevelValues.GridIndex)];
        }

        private void AddAutoMatches(Grid grid, AssistParams assistParams, List<TileKinds> usedKinds)
        {
            var diff = assistParams?.WinLossValueDiff();
            var willGetHelp = diff <= 0;
            if (willGetHelp && _autoMatchesCoolDown > 0)
            {
                _autoMatchesCoolDown--;
                return;
            }

            if (_autoMatchesCoolDown <= 0) 
                SetupAutoMatchesCoolDown(assistParams);

            var autoMatchesThreshold = AssistParams.AimAtWinningLevel
                ? assistParams.AssistSystemConfig.AimingToWin.Value.AutomatchHelpThreshold
                : assistParams.AssistSystemConfig.AimingToLose.Value.AutomatchHelpThreshold;
            var autoMatchesLimit = Mathf.Min(assistParams.MaxAutoMatchesInDefinition, (int)Math.Floor(diff.Value / autoMatchesThreshold));
            if (autoMatchesLimit <= 0) return;
            
            var autoMatchAssist = new AssistInputAutomatches(autoMatchesLimit, assistParams.GoalValueWeights);
            PrepareAutoMatches(grid, usedKinds, autoMatchAssist);
            TryApplyAutoMatches(grid, null);
        }

        private void CopyDefinitions(Grid copySourceGrid, Grid copyDestGrid, IRootSimulationHandler simHandler)
        {
            var undefinedTileCoords = new List<Coords>();

            foreach (var cell in copyDestGrid.Cells)
            {
                if (cell.Tile != null && cell.Tile.Kind == TileKinds.Undefined)
                {
                    undefinedTileCoords.Add(cell.Coords);
                }
            }

            foreach (var coord in undefinedTileCoords)
            {
                var destCell = copyDestGrid.GetCell(coord);
                var undefinedTile = destCell.Tile;
                var tile = copySourceGrid.GetCell(coord).Tile;

                if (tile.Kind.IsColored())
                {
                    undefinedTile.SetKind(tile.Kind);
                    simHandler?.OnTileKindDefined(tile.Id, tile.Kind);
                }
                else
                {
                    M3Debug.LogError("Not colored tile is spawned during definition");
                }

#if BBB_LOG
                if (tile.Kind == 0 && tile.Speciality == TileSpeciality.None)
                {
                    M3Debug.LogError("None-tile is detected during CopyDefinitions");
                }
#endif
            }
        }
    }
}