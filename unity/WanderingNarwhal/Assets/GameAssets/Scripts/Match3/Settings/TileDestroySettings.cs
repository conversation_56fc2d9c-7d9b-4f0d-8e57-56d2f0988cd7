using System;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Settings
{
    [Serializable]
    public class TileDestroySettings
    {
        public float Duration = 0.15f;
        [Tooltip("Defines for how long till will be occupied by invisible blocker tile")]
        public float BusyTime = 0.385f;
        public float TargetScale = 0.375f;
        public AnimationCurve ScaleCurve;
        [Tooltip("This curve defines tiles position change to a single position")]
        public AnimationCurve MergeCurve;
    }
}