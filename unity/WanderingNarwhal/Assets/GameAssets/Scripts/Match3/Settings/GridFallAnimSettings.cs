using System;
using DG.Tweening;

namespace GameAssets.Scripts.Match3.Settings
{
    [Serializable]
    public struct GridFallAnimSettings
    {
        public float StartDelay;
        public float MinX;
        public float MaxX;
        public float EndY;
        public float Duration;
        public Ease EaseX;
        public Ease EaseY;
        public float OvershootX;
        public float OvershootYMin;
        public float OvershootYMax;
        public float RotationMin;
        public float RotationMax;
        public string StartSoundFxUid;
    }
}