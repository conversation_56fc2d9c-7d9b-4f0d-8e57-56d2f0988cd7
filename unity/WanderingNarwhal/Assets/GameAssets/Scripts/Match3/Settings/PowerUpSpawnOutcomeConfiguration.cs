using System;
using System.Collections.Generic;
using BBB;
using BBB.Core;
using Core.Configs;
using FBConfig;

namespace GameAssets.Scripts.Match3.Settings
{
    public class PowerUpSpawnOutcomeConfiguration
    {
        public Dictionary<TileAsset, RewardConfiguration> PowerUpSpawnOutcomeConfigurations;

        public class RewardConfiguration
        {
            public Dictionary<TileAsset, float> RewardConfigurations;
            public int MaxSpawnCount;
            public bool AllowSameType;
        }

        public PowerUpSpawnOutcomeConfiguration(IDictionary<string, PowerUpSpawnOutcomeConfig> outcomeConfigDictionary)
        {
            PowerUpSpawnOutcomeConfigurations = new Dictionary<TileAsset, RewardConfiguration>();

            if (outcomeConfigDictionary == null || outcomeConfigDictionary.Count == 0)
            {
                BDebug.LogError(LogCat.Match3, "PowerUpSpawnConfiguration: outcomeConfigDictionary is null.");
                return;
            }

            foreach (var kvp in outcomeConfigDictionary)
            {
                var config = kvp.Value;
                var distributionDict = FlatBufferHelper.ToDict(config.Distribution, config.DistributionLength);
                var rewardConfigurations = new Dictionary<TileAsset, float>();

                foreach (var pair in distributionDict)
                {
                    if (string.IsNullOrEmpty(pair.Key))
                    {
                        BDebug.LogError(LogCat.Match3, "PowerUpSpawnConfiguration: key is null.");
                        continue;
                    }

                    if (Enum.TryParse(pair.Key, out TileAsset rewardType))
                    {
                        rewardConfigurations.TryAdd(rewardType, pair.Value);
                    }
                    else
                    {
                        BDebug.LogError(LogCat.Match3, $"PowerUpSpawnConfiguration: Unable to parse key '{pair.Key}'.");
                    }
                }

                PowerUpSpawnOutcomeConfigurations.TryAdd((TileAsset)Enum.Parse(typeof(TileAsset), config.Uid),
                    new RewardConfiguration
                    {
                        RewardConfigurations = rewardConfigurations,
                        MaxSpawnCount = config.Count,
                        AllowSameType = config.AllowSameType
                    });
            }
        }
    }
}