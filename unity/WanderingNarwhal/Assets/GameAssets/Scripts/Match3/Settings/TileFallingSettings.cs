using System;
using UnityEngine;

namespace GameAssets.Scripts.Match3.Settings
{
    [Serializable]
    public class TileFallingSettings
    {
        [SerializeField] private float _startFallingSpeed;
        [SerializeField] private float _maxFallingSpeed;
        [SerializeField] private float _timeToReachMaxSpeed;
        [SerializeField] private AnimationCurve _startToMaxSpeedCurve;
        [SerializeField] private float _timeToReleaseCell;
            
        public float StartFallingSpeed => _startFallingSpeed;
        public float MaxFallingSpeed => _maxFallingSpeed;
            
        public float TimeToReachMaxSpeed => _timeToReachMaxSpeed;
        public float TimeToReleaseCell => _timeToReleaseCell;
        public AnimationCurve StartToMaxSpeedCurve => _startToMaxSpeedCurve;
    }
}