using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class DidiHatLayerView : TileLayerViewBase
    {
        public DidiHatLayerView(ITileLayer layer) : base(layer) { }

        private DidiHatLayerRenderer _renderer;

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<DidiHatLayerRenderer>();
        }
    }
}