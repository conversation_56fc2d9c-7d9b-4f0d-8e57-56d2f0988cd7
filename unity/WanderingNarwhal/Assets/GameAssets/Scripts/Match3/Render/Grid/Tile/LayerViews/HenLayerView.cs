using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class HenLayerView : SheetTileLayerViewBase
    {
        private HenLayerRenderer _renderer;

        public HenLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<HenLayerRenderer>();
            ProcessRendererVisibilityOnInstantiateView();
        }

        protected override void ShowRenderer()
        {
            if (_renderer != null) _renderer.OnSpawn();
        }

        protected override void HideRenderer()
        {
            if (_renderer != null) _renderer.Hide();
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            _renderer.SetLayer(newLevel);
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                {
                    IsPlayingLayerViewAnimation = true;

                    void OnDone()
                    {
                        IsPlayingLayerViewAnimation = false;
                    }

                    _renderer.PlayDestroy(onDone: OnDone);
                }
                    break;
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayHenAppear();
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            ProcessRendererVisibilityOnApplyStarted(isVisible);
            base.Apply(tile, coords, container, viewsList, isVisible);
        }
    }
}