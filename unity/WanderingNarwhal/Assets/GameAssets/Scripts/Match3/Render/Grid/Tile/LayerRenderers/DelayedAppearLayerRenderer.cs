using BebopBee.Core;
using BebopBee.Core.Audio;
using DG.Tweening;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    /// <summary>
    /// Tile layer renderer base class for tiles that
    /// starts invisible on grid and appear with small delay after level start.
    /// </summary>
    /// <remarks>
    /// When game is starting, special animation is triggered in tile layer : "CustomAppear", and tile layer will call PlayAppear in renderer.
    ///
    /// This component designed for non-spawnable tiles, so custom appear logic will not work for tiles, spawned after the start of level.
    /// </remarks>
    public class DelayedAppearLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private string _appearSfxUid = "";

        [SerializeField]
        [Range(0f, 3f)]
        private float _appearDelay = 1f;

        [SerializeField]
        protected bool _hideUntilAppear = true;

        protected bool IsPlayedAppear;

        private Tweener _playAppearAfterDelayTween;
        
        
        protected virtual void Awake()
        {
#if UNITY_EDITOR
            if (_hideUntilAppear && BBB.M3Editor.M3Editor.IsCurrentSceneLevelEditorScene())
            {
                // Reveal animation doesn't play in level editor, so it should not be hidden by default.
                _hideUntilAppear = false;
            }
#endif
        }
        
        public virtual void ResetToDefault()
        {
            IsPlayedAppear = false;
        }

        public virtual void OnRefresh()
        {
            if (!IsPlayedAppear && _hideUntilAppear)
            {
                Hide();
            }
        }

        protected virtual void Hide()
        {
        }

        public override void Show()
        {
        }

        public override void PlayAppear()
        {
            if (_appearDelay >= 0)
            {
                ResetPlayAppearAfterDelayTween();
                _playAppearAfterDelayTween = Rx.Invoke(_appearDelay, PlayAppearAfterDelay);
            }
            else
            {
                PlayAppearAfterDelay();
            }
        }

        private void ResetPlayAppearAfterDelayTween()
        {
            _playAppearAfterDelayTween?.Kill();
            _playAppearAfterDelayTween = null;
        }

        private void PlayAppearAfterDelay(long arg = 0)
        {
            if (this == null)
                return;

            OnAppearAfterDelayBeforeBaseCall();
            DoAppear();
            OnAppearAfterDelayAfterBaseCall();
        }
        
        private void DoAppear()
        {
            if (_hideUntilAppear && !IsPlayedAppear)
            {
                IsPlayedAppear = true;
                AudioProxy.PlaySound(_appearSfxUid);
            }
            Show();
        }
        
        protected virtual void OnAppearAfterDelayBeforeBaseCall() { }
        
        protected virtual void OnAppearAfterDelayAfterBaseCall() { }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            ResetPlayAppearAfterDelayTween();
        }
    }
}