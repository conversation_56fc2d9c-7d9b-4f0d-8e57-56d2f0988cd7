using System;
using System.Collections;
using BBB.Audio;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Utils;
using Spine;
using Spine.Unity;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class HiveLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _hitAnimName = "Match";

        [SerializeField]
        private string _idleAnimName = "Idle";

        [SerializeField]
        private string _idleOutOfBeesAnimName = "IdleOutOfBees";

        [SerializeField]
        private string _outOfBees = "OutOfBees";

        [SerializeField]
        private string _spawnAnimName;

        [SerializeField]
        private string _tapFeedbackName = "Match";

        [SerializeField]
        private string _visitorPreviewStateName = "";

        private Action _currentOnDestroyCallback;

        private bool _isOutOfBees = false;
        private bool _outOfBeesAnimationPlaying;

        public bool IsOutOfBees
        {
            get { return _isOutOfBees; }
            set
            {
                _isOutOfBees = value;

                if (!_isOutOfBees) return;
                if(_outOfBeesAnimationPlaying) return;
                _outOfBeesAnimationPlaying = true;
                _sk.AnimationState.SetAnimation(0, _outOfBees, loop: false);
                _sk.AnimationState.AddAnimation(0, _idleOutOfBeesAnimName, loop: true, 0.9f);
            }
        }
        
        public void InitialSetup()
        {
            _isOutOfBees = false;
            _outOfBeesAnimationPlaying = false;
        }

        public void PlayHiveAppear()
        {
            _isOutOfBees = false;
            _outOfBeesAnimationPlaying = false;
            if (!_idleAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
            }
        }

        public void PlayIdle()
        {
            if (_isOutOfBees)
            {
                var current = _sk.AnimationState.GetCurrent(0);
                if (current != null && current.Animation != null && current.Animation.Name == _hitAnimName)
                {
                    // Skip idle if currently playing hit, which will then switch to idle by itself.
                }
                else
                {
                    if (!_outOfBeesAnimationPlaying)
                    {
                        _outOfBeesAnimationPlaying = true;
                        _sk.AnimationState.SetAnimation(0, _outOfBees, loop: false);
                        _sk.AnimationState.AddAnimation(0, _idleOutOfBeesAnimName, loop: true, 0.9f);
                        
                    }
                }
            }
            else
            {
                _sk.AnimationState.SetAnimation(0, _idleAnimName, loop: true);
            }
        }

        public void PlayHit()
        {
            if (!_isOutOfBees)
            {
                AudioProxy.PlaySound(Match3SoundIds.BeeHiveHit);
                
                _outOfBeesAnimationPlaying = false;
                _sk.AnimationState.SetAnimation(0, _hitAnimName, loop: false);
                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);

                _sk.AnimationState.Start -= OnNextAnimStart;
                _sk.AnimationState.Start += OnNextAnimStart;
            }
            else
            {
                PlayIdle();
            }
        }

        private void OnNextAnimStart(TrackEntry trackentry)
        {
            if (trackentry.Animation.Name == _idleAnimName)
            {
                _sk.AnimationState.Start -= OnNextAnimStart;
                if (_isOutOfBees)
                {
                    _outOfBeesAnimationPlaying = true;
                    _sk.AnimationState.SetAnimation(0, _outOfBees, loop: false);
                    _sk.AnimationState.AddAnimation(0, _idleOutOfBeesAnimName, loop: true, 0.9f);
                }
            }
            else if (trackentry.Animation.Name == _idleOutOfBeesAnimName)
            {
                _sk.AnimationState.Start -= OnNextAnimStart;
                if (!_isOutOfBees)
                {
                    _sk.AnimationState.SetAnimation(0, _idleAnimName, loop: true);
                }
            }
        }

        public void PlayDestroy(Action onDone)
        {
            _currentOnDestroyCallback = onDone;
            StartCoroutine(DestroyCallbackRoutine(0.1f));
        }

        private IEnumerator DestroyCallbackRoutine(float delay)
        {
            yield return WaitCache.Seconds(delay);
            _currentOnDestroyCallback?.Invoke();
            _currentOnDestroyCallback = null;
        }

        protected override void OnDisable()
        {
            if (_currentOnDestroyCallback != null)
            {
                _currentOnDestroyCallback.Invoke();
                _currentOnDestroyCallback = null;
            }

            IsOutOfBees = false;
        }

        public void OnSpawn()
        {
            UnHide();
            if (!_spawnAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.ClearTracks();
                _sk.AnimationState.AddAnimation(0, _spawnAnimName, loop: false, delay: 0);
            }

            if (!_idleAnimName.IsNullOrEmpty())
            {
                if (_spawnAnimName.IsNullOrEmpty())
                {
                    _sk.AnimationState.ClearTracks();
                }

                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
            }
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            if (_tapFeedbackName.IsNullOrEmpty()) return;
            if (_isOutOfBees) return;

            _sk.AnimationState.SetAnimation(0, _tapFeedbackName, loop: false);
            if (!_idleAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
            }
        }

        public override void PlayVisitorPreview()
        {
            if (!_visitorPreviewStateName.IsNullOrEmpty() && _sk != null)
            {
                if (_sk.AnimationState == null)
                {
                    _sk.Initialize(false);
                }

                _sk.AnimationState.SetAnimation(0, _visitorPreviewStateName, loop: false);
            }
        }

        public void Hide()
        {
            _sk.gameObject.SetActive(false);
        }

        private void UnHide()
        {
            _sk.gameObject.SetActive(true);
        }
    }
}