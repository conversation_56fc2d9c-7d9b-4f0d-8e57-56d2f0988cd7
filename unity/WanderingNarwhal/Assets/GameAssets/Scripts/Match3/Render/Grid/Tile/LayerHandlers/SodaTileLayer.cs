namespace BBB.Match3.Renderer
{
    public class SodaTileLayer : TileLayerBase
    {
        public override TileLayerState State => TileLayerState.Soda;

        protected override bool IsCondition(TileLayerState state)
        {
            return (state & TileLayerState.Soda) != 0;
        }

        /// <summary>
        /// Convert integer number to array of colors for each soda inside container.
        /// </summary>
        /// <param name="stateNum">Source integer.</param>
        /// <param name="output">Output array with max size length.</param>
        /// <param name="count">Count of items that must be less or equal to max size.</param>
        public static void ExtractTileKindsFromInt(int stateNum, TileKinds[] output, int count)
        {
            for (var i = 0; i < count; i++)
            {
                var colorNum = GetColorNumFromState(stateNum, i);
                var color = EncodedNumToColor(colorNum);
                output[i] = color;
            }
        }

        /// <summary>
        /// Convert 3-bit number to tile color.
        /// </summary>
        public static TileKinds EncodedNumToColor(int bitMaskNum)
        {
            var masked = bitMaskNum & 0b111;
            var result = (TileKinds) masked;
            if (result == 0) result = TileKinds.Undefined;
            return result;
        }

        /// <summary>
        /// Convert tile color to 3-bit number.
        /// </summary>
        public static int ColorToEncodedNum(TileKinds color)
        {
            var result = (int) color;
            if (result < 0) result = 0;
            return result;
        }

        /// <summary>
        /// Set 3 bit number in 32-bit integer at specific position with given offset.
        /// </summary>
        public static int SetColorNumInState(int state, int colorNum, int index)
        {
            if (index < 0 || index >= 4) return state;
            var offset = index * 3;

            // Erase 3 bits in state number at target position.
            state &= (~(0b111 << offset));

            // Write 3 bits in target spot.
            var offsetNum = colorNum << offset;
            state |= offsetNum;
            return state;
        }

        /// <summary>
        /// Take 3 bits number at specific offset in 32-bit integer.
        /// </summary>
        public static int GetColorNumFromState(int state, int index)
        {
            if (index < 0 || index >= 4) return state;

            var offset = index * 3;
            var result = (state & (0b111 << offset)) >> offset;
            return result;
        }

        public static bool IsColorStateExistInState(int state, int colorNum)
        {
            for (var i = 0; i < 4; i++)
            {
                var subState = GetColorNumFromState(state, i);
                if (subState == colorNum)
                {
                    return true;
                }
            }

            return false;
        }
    }
}
