using UnityEngine;

namespace BBB.Match3.Renderer
{
    public struct TileViewCacheInfo
    {
        public readonly Vector3 SpecialRendererGlobalScale;
        public readonly Vector3 LocalPosition;
        public readonly Vector3 GlobalPosition;
        
        public TileViewCacheInfo(TileView tileView)
        {
            SpecialRendererGlobalScale = tileView.GetSpecialRendererGlobalScale();
            LocalPosition = tileView.LocalPosition;
            GlobalPosition = tileView.GlobalPosition;
        }
    }
}