using System;
using BBB.Audio;
using BebopBee.Core;
using BebopBee.Core.Audio;
using Spine.Unity;
using UnityEngine;
using Random = UnityEngine.Random;

namespace BBB.Match3.Renderer
{
    public class MoneyBagLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _idleName = "Idle";

        [SerializeField]
        private string _appearName = "Appear";

        [SerializeField]
        private string _matchName = "Match";

        [SerializeField]
        private string _tapFeedbackName = "Tap";

        [SerializeField] private float _diappearAnimCutoffRatio = 0.7f;

        public void Activate()
        {
            _sk.enabled = true;
        }

        public void PlayIdle()
        {
            _sk.AnimationState.SetAnimation(0, _idleName, loop: true);
            _sk.AnimationState.AddAnimation(0, _idleName, loop: true, delay: Random.Range(0f, 3f));
        }

        public void PLayAppear()
        {
            if (string.IsNullOrEmpty(_appearName))
            {
                _sk.AnimationState.SetAnimation(0, _idleName, loop: false);
                return;
            }

            _sk.AnimationState.SetAnimation(0, _appearName, loop: false);
            _sk.AnimationState.AddAnimation(0, _idleName, loop: true, delay: 0);
        }

        public void PlayDestroy(Action onDone)
        {
            var anim = _sk.SkeletonData.Animations.Find(a => a.Name == _matchName);
            _sk.AnimationState.SetAnimation(0, _matchName, loop: false);
            AudioProxy.PlaySound(Match3SoundIds.MoneyBagDestroy);
            //this hack is needed to hide an artifact in the end of disappear animation without
            //changing the asset itself
            Rx.Invoke(anim.Duration*_diappearAnimCutoffRatio, _ =>
            {
                _sk.enabled = false;
                onDone.SafeInvoke();
            });
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            base.PlayTapFeedback(layerView);
            if (string.IsNullOrEmpty(_tapFeedbackName)) return;
            _sk.AnimationState.SetAnimation(0, _tapFeedbackName, loop: false);
            _sk.AnimationState.AddAnimation(0, _idleName, loop: true, delay: 0);
        }
    }
}