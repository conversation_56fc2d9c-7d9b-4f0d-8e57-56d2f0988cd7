using System;
using JetBrains.Annotations;

namespace BBB.Match3.Renderer
{
    public class ToadAnimatorEventHandler : BbbMonoBehaviour
    {
        public Action BaseDestroyed;

        [UsedImplicitly]
        private void OnBaseDestroyed()
        {
            BaseDestroyed.SafeInvoke();
            BaseDestroyed = null;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            BaseDestroyed = null;
        }
    }
}