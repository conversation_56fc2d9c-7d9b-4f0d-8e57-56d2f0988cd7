using System;
using BBB.Audio;
using BebopBee.Core;
using BebopBee.Core.Audio;
using Spine;
using Spine.Unity;
using UnityEngine;
using BBB.Core;
using Random = UnityEngine.Random;

namespace BBB.Match3.Renderer
{
    public class MonkeyBaseLayerRenderer : DelayedAppearLayerRenderer
    {
        [SerializeField]
        private SkeletonGraphic _sk;

        [SerializeField]
        private string _appearAnimName = "Appear";

        [SerializeField]
        private string _hitAnimName = "Match";

        [SerializeField]
        private string _afterHit1AnimName = "Lost_banana";

        [SerializeField]
        private string _afterHit2AnimName = "Get_banana";

        [SerializeField]
        private string _idleAnimName = "Idle";

        [SerializeField]
        private string _destroyAnimName = "Destroy";

        [SerializeField]
        private string _tapFeedbackName = "Tap";

        [SerializeField]
        private string _previewAnimName = "";

        [SerializeField]
        private string _destroySoundUid;

        [Range(0f, 3f)]
        [SerializeField]
        private float _destroySoundDelay = 0.5f;

        [SerializeField]
        private string[] _layersNames = new string[] { "1_banana", "2_banana", "3_banana" };

        [SerializeField]
        private ActivePoolItem _destroyObject;

        private Action _currentOnDestroyCallback;

        [SerializeField]
        private int _currentLevel;

        [SerializeField]
        private string _visitorPreviewStateName = "InfoScreen";
        
        [SerializeField]
        private int _sortingOrderChangeOnDestroy = 10;

        public virtual void Setup()
        {
            if (_sk == null) return;
           
            _sk.Initialize(true);
            _sk.AnimationState.ClearTracks();
            _sk.Skeleton.SetToSetupPose();
        }
        
        public void SetLayer(int layer)
        {
            _currentLevel = layer;
            OnRefresh();
        }

        public override void OnRefresh()
        {
            base.OnRefresh();
            if (!_hideUntilAppear || IsPlayedAppear)
            {
                RefreshSkin();
                PlayIdle();
            }
        }

        private void RefreshSkin()
        {
            var skinName = _layersNames[Clamp(_currentLevel - 1, 0, _layersNames.Length)];
            var skin = _sk.SkeletonData.FindSkin(skinName);
            _sk.Skeleton.SetSkin(skin);
            _sk.Skeleton.SetToSetupPose();
        }

        protected override void OnAppearAfterDelayAfterBaseCall()
        {
            if (_sk != null)
            {
                _sk.AnimationState.SetAnimation(0, _appearAnimName, loop: false);
                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: Random.Range(0f, 3f));
            }
        }

        public override void Show()
        {
            base.Show();
            if (_sk != null)
            {
                _sk.gameObject.SetActive(true);
            }
        }

        protected override void Hide()
        {
            base.Hide();
            if (_sk != null)
            {
                _sk.gameObject.SetActive(false);
            }
        }

        private void PlayIdle()
        {
            _sk.AnimationState.SetAnimation(0, _idleAnimName, loop: true);
            _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, Random.Range(0f, 3f));
        }

        public virtual void PlayHit()
        {
            _sk.AnimationState.SetAnimation(0, _hitAnimName, loop: false);
            _sk.AnimationState.AddAnimation(0, _afterHit1AnimName, loop: false, 0);
            _sk.AnimationState.AddAnimation(0, _afterHit2AnimName, loop: false, 0);
            _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, 0);
            AudioProxy.PlaySound(Match3SoundIds.MonkeyHit);
        }

        public void PlayDestroy(Action onDone)
        {
            _currentOnDestroyCallback = onDone;
            if (_sortingOrderChangeOnDestroy != 0)
            {
                var canvas = GetComponent<Canvas>();
                if (canvas != null)
                {
                    canvas.sortingOrder += _sortingOrderChangeOnDestroy;
                }
            }
            
            _sk.AnimationState.SetAnimation(0, _hitAnimName, loop: false);
            _sk.AnimationState.AddAnimation(0, _destroyAnimName, loop: false, delay: 0);
            _sk.AnimationState.Complete -= OnDestroyAnimCallback;
            _sk.AnimationState.Complete += OnDestroyAnimCallback;
            if (_destroySoundDelay > 0)
            {
                Rx.Invoke(_destroySoundDelay, PlayDestroySound);
            }
            else
            {
                PlayDestroySound();
            }
        }

        private void PlayDestroySound(long arg = 0)
        {
            AudioProxy.PlaySound(_destroySoundUid);
        }

        private void OnDestroyAnimCallback(TrackEntry entry)
        {
            _sk.AnimationState.Complete -= OnDestroyAnimCallback;
            if (entry.Animation.Name == _hitAnimName)
            {
                DestroyObject();
            }
            else if (entry.Animation.Name == _destroyAnimName)
            {
                _currentOnDestroyCallback?.Invoke();
                _currentOnDestroyCallback = null;
            }
        }

        protected virtual void DestroyObject()
        {
            if (_destroyObject != null)
            {
                _destroyObject.OnSpawn();
            }
        }

        protected override void OnDisable()
        {
            ResetToDefault();
            
            if (_sk != null && _sk.AnimationState != null)
            {
                _sk.AnimationState.Complete -= OnDestroyAnimCallback;
            }

            if (_currentOnDestroyCallback != null)
            {
                _currentOnDestroyCallback.Invoke();
                _currentOnDestroyCallback = null;
            }
            if (_destroyObject != null)
            {
                _destroyObject.OnRelease();
            }
        }

        public override void PlayTapFeedback(ITileLayerView layerView)
        {
            if (_tapFeedbackName.IsNullOrEmpty()) return;

            _sk.AnimationState.SetAnimation(0, _tapFeedbackName, loop: false);
            if (!_idleAnimName.IsNullOrEmpty())
            {
                _sk.AnimationState.AddAnimation(0, _idleAnimName, loop: true, delay: 0);
            }
        }

        public override void PlayPreview()
        {
            Show();
            if (_previewAnimName.IsNullOrEmpty())
            {
                PlayIdle();
            }
            else
            {
                _sk.AnimationState.SetAnimation(0, _previewAnimName, loop: true);
            }
        }

        public override void PlayVisitorPreview()
        {
            if (!_visitorPreviewStateName.IsNullOrEmpty() && _sk != null)
            {
                if (_sk.AnimationState == null)
                {
                    _sk.Initialize(false);
                }

                _currentLevel = 1;
                RefreshSkin();
                _sk.AnimationState.SetAnimation(0, _visitorPreviewStateName, loop: false);
            }
        }

        private static int Clamp(int n, int a, int b)
        {
            return n < a ? a : (n >= b ? b - 1 : n);
        }
    }
}