using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SodaLayerView : SheetTileLayerViewBase
    {
        private SodaLayerRenderer _renderer;
        private int _currentState;
        private int _size;
        private TileKinds[] _currentColors;
        private int _currentCount;
        
        public SodaLayerView(ITileLayer layer) : base(layer)
        {
            
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<SodaLayerRenderer>();
            _renderer.InitialSetup();
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

         protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            RefreshTile(tile, coords);
        }
        
        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            if (!Applied)
            {
                base.Apply(tile, coords, container, viewsList, isVisible);
                _currentCount = tile.GetParam(TileParamEnum.SodaBottlesCount);;
                _currentState = tile.GetParam(TileParamEnum.SodaColors);
                _currentColors = new TileKinds[_currentCount];
                SodaTileLayer.ExtractTileKindsFromInt(_currentState, _currentColors, _currentCount);
                _renderer.Setup(_currentColors);
            }
            else
            {
                RefreshTile(tile, coords);
            }
        }

        private void RefreshTile(Tile tile,  Coords? coords = null)
        {
            if (_currentState == 0)
                return;
            
            var count = tile.GetParam(TileParamEnum.SodaBottlesCount);
            var state = tile.GetParam(TileParamEnum.SodaColors);
            
            if (state != _currentState)
            {
                for (var i = 0; i < 4; i++)
                {
                    var currentSubColor = SodaTileLayer.GetColorNumFromState(_currentState, i);
                    var newSubColor = SodaTileLayer.GetColorNumFromState(state, i);
                    if (currentSubColor != newSubColor)
                    {
                        _renderer.PlayHit(i);
                        AudioProxy.PlaySound(Match3SoundIds.SodaBottlesBreak);
                        FxRenderer.SpawnSodaBottleColorFx(
                            _renderer.ParticleOffsetCoords,
                            FxType.SodaBottleDestroy,
                            new FxOptionalParameters() { col = _renderer.BottleColor },
                            _renderer.DestroyBottleFXDuration);
                    }
                }
            }
            _currentCount = count;
            _currentState = state;
        }
        
        public override void Animate(Coords coords, TileLayerViewAnims anim, TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
        {
            base.Animate(coords, anim, animParams);
            switch (anim)
            {
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;

                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;

                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;
                    AudioProxy.PlaySound(Match3SoundIds.SodaBottlesDestroy);
                    FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.SodaTileDestroy, _renderer.DestroyTileFXDuration);
                    _renderer.PlayDestroy(() => { IsPlayingLayerViewAnimation = false; });
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
            }
        }
    }
}
