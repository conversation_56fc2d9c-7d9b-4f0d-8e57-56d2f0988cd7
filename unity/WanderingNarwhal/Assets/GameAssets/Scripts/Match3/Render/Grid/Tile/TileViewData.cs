using System;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    [Serializable]
    public class TileViewData
    {
        public TileKinds TileKinds;
        public Sprite Sprite;
        public Color Color;
        public Material PieceMaterial;
        public Vector2 CellSize;
    }

    [Serializable]
    public class ColorCrateViewData
    {
        public TileKinds Kind;
        public Color Color = Color.white;
        public Color ExplosionColor = Color.white;
        public Sprite MainSprite;
        public Sprite SecondarySprite;
    }
}