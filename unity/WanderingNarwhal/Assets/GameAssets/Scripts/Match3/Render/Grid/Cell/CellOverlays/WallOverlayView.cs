using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class WallOverlayView : CellOverlayView
    {
        private const float THINNING_FACTOR = 0.3f;
        public Sprite[] DestructibleWall = new Sprite[3];
        
        public override void Init(ICellLayer layer)
        {
            Refresh(layer);

            var wallRectTransform = SpriteRenderer.GetComponent<RectTransform>();
            
            var state = layer.State;

            var rendererTransform = GetComponent<RectTransform>();
            switch (state)
            {
                case CellLayerState.WallTop:
                case CellLayerState.WallBottom:
                {
                    var sizeDelta = rendererTransform.sizeDelta;
                    var fullHeight = sizeDelta.y;
                    var height = fullHeight * THINNING_FACTOR;
                    sizeDelta.y = height;
                    rendererTransform.sizeDelta = sizeDelta;

                    if (state == CellLayerState.WallTop)
                    {
                        var euler = rendererTransform.localRotation.eulerAngles;
                        euler.z = 180;
                        rendererTransform.localRotation = Quaternion.Euler(euler);
                        wallRectTransform.localRotation = Quaternion.Euler(euler);
                    }

                    var mult = state == CellLayerState.WallBottom ? -1 : 1;

                    rendererTransform.localPosition += new Vector3(0f, mult * fullHeight / 2f, 0f);
                    break;
                }
                case CellLayerState.WallLeft:
                case CellLayerState.WallRight:
                {
                    var mult = state == CellLayerState.WallLeft ? -1 : 1;

                    var sizeDelta = rendererTransform.sizeDelta;
                    var fullHeight = sizeDelta.y;
                    var height = fullHeight * THINNING_FACTOR;
                    sizeDelta.y = height;
                    rendererTransform.sizeDelta = sizeDelta;

                    var euler = rendererTransform.localRotation.eulerAngles;
                    euler.z = mult * 90;
                    rendererTransform.localRotation = Quaternion.Euler(euler);

                    if (mult == 1)
                    {
                        euler.z = 180;
                        wallRectTransform.localRotation = Quaternion.Euler(euler);
                    }
                    rendererTransform.localPosition += new Vector3(mult * fullHeight / 2f, 0f, 0f);
                    break;
                }
            }
            
            base.Init(layer);
        }

        private void Refresh(ICellLayer layer)
        {
            SpriteRenderer.sprite = layer.OverlayType == CellOverlayType.DestructibleWallsOverlay ? DestructibleWall[layer.WallCount - 1] : layer.Sprite;
        }
        
        public override void OnUpdate(ICellLayer layer)
        {
            Refresh(layer);
        }
    }
}
