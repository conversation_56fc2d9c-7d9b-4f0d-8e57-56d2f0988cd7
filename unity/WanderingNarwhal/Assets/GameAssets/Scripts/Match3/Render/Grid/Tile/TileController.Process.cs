using UnityEngine;

namespace BBB.Match3.Renderer
{
    public partial class TileAnimator
    {
        private class AnimationProcess
        {
            private readonly TileAnimatorState _state;
            private readonly Animator _animator;

            private static bool _hashesCached;
            private static int _destroyHash;
            private static int _hintHash;
            private static int _isDestroyHash;
            private static int _fallingHash;
            private static int _settleHash;
            private static int _shakingHash;
            private static int _horizontalShockHash;
            private static int _verticalShockHash;
            private static int _diagonalShockHash;
            private static int _subtleShockHash;
            private static int _destroyFallingBlendValueHash;

            public AnimationProcess(Animator animator)
            {
                _animator = animator;
                _state = new TileAnimatorState();
                CacheAnimationsHashes();
            }

            public bool IsAnyPlaying(StateType state)
            {
                return (_state.CurrentType & state) != 0;
            }

            private static void CacheAnimationsHashes()
            {
                if (_hashesCached)
                    return;

                _hintHash = Animator.StringToHash("Hint");
                _destroyHash = Animator.StringToHash("Destroy");
                _isDestroyHash = Animator.StringToHash("IsDestroying");
                _destroyFallingBlendValueHash = Animator.StringToHash("DestoryFallingBlendValue");
                _fallingHash = Animator.StringToHash("Falling");
                _settleHash = Animator.StringToHash("Settle");
                _shakingHash = Animator.StringToHash("Shaking");
                _horizontalShockHash = Animator.StringToHash("HorizontalShock");
                _verticalShockHash = Animator.StringToHash("VerticalShock");
                _diagonalShockHash = Animator.StringToHash("DiagonalShock");
                _subtleShockHash = Animator.StringToHash("SubtleShock");

                _hashesCached = true;
            }

            public void Execute(StateType stateType, object arg = null)
            {
                bool applied = true;
                
                switch (stateType)
                {
                    case StateType.Fall:
                        if ((_state.CurrentType & StateType.Fall) != StateType.Fall)
                        {
                            Fall();
                        }
                        else applied = false;
                        break;

                    case StateType.Settle:
                        Settle();
                        break;

                    case StateType.Shaking:
                        _animator.SetBool(_shakingHash, true);
                        break;
                    
                    case StateType.Destroy:
                        Destroy();
                        break;
                    case StateType.Hint:
                        Hint();
                        break;
                    case StateType.SubtleShockwave:
                        if ((StateType.SubtleShockwaveCheck & _state.CurrentType) == 0)
                        {
                            StartSubtleShockwaveEffect();
                        }
                        else applied = false;
                        break;

                    case StateType.Shockwave:
                        if ((StateType.ShockwaveCheck & _state.CurrentType) == 0)
                        {
                            StartShockwaveEffect((CardinalDirections) arg);
                        }
                        else applied = false;
                        break;
                }

                if (applied)
                    _state.CurrentType |= stateType;
            }

            public void Finish(StateType stateType)
            {
                switch (stateType)
                {
                    case StateType.Destroy:
                        _animator.SetBool(_isDestroyHash, false);
                        break;
                    case StateType.Shockwave:
                        _animator.ResetTrigger(_subtleShockHash);
                        _animator.ResetTrigger(_diagonalShockHash);
                        _animator.ResetTrigger(_horizontalShockHash);
                        _animator.ResetTrigger(_verticalShockHash);
                        break;

                    case StateType.Shaking:
                        _animator.SetBool(_shakingHash, false);
                        break;
                }

                _state.CurrentType &= ~stateType;
            }

            public void Release()
            {
                _state.CurrentType = StateType.None;
            }

            private void Fall()
            {
                _animator.enabled = true;
                if (_animator.GetBool(_isDestroyHash))
                {
                    _animator.SetFloat(_destroyFallingBlendValueHash, 0.25f);
                }
                else
                {
                    _animator.SetTrigger(_fallingHash);
                }
            }

            private void Settle()
            {
                _animator.enabled = true;
                _state.CurrentType ^= StateType.Fall;
                _animator.SetTrigger(_settleHash);
            }

            private void Destroy()
            {
                _animator.enabled = true;
                _animator.SetTrigger(_destroyHash);
                _animator.SetBool(_isDestroyHash, true);
            }

            private void Hint()
            {
                _animator.enabled = true;
                _animator.SetTrigger(_hintHash);
            }

            private void StartShockwaveEffect(CardinalDirections fromDirection)
            {
                var trigger = _diagonalShockHash;
                var simplifyDir = fromDirection.GetSimplifiedDirections();

                switch (simplifyDir)
                {
                    case SimplifiedDirections.Vertical:
                        trigger = _verticalShockHash;
                        break;
                    case SimplifiedDirections.Horizontal:
                        trigger = _horizontalShockHash;
                        break;
                }

                _animator.enabled = true;
                _animator.SetTrigger(trigger);
            }

            private void StartSubtleShockwaveEffect()
            {
                _animator.enabled = true;
                _animator.SetTrigger(_subtleShockHash);
            }
        }
    }
}
