using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class VerticalBreakerTileLayer : TileLayerBase
    {
        public override TileLayerState State { get { return TileLayerState.VerticalLb; } }

        public override void ConfigurateRenderer(Tile tile, RectTransform rendererTransform)
        {
            rendererTransform.localRotation = Quaternion.Euler(0f,0f,90f);
        }
        
        protected override bool IsCondition(TileLayerState state)
        {
            return (state & State) != 0;
        }
    }
}