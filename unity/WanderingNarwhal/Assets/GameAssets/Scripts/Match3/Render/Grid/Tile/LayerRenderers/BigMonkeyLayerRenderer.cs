using Bebopbee.Core.Extensions.Unity;
using BebopBee.UnityEngineExtensions;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class BigMonkeyLayerRenderer : MonkeyBaseLayerRenderer
    {
        [SerializeField] private Animator _boxAnimator;
        [SerializeField] private float _defaultAnchorMax = 2.15f;
        
        private static readonly int DestroyTile = Animator.StringToHash("Destroy");
        private static readonly int ShakeTile = Animator.StringToHash("Shake");
        
        public override void Setup()
        {
            _boxAnimator.ResetAllParameters();
            
            Content.anchorMin = Vector2.zero;
            Content.anchorMax = Vector2.one * _defaultAnchorMax;
            Content.offsetMin = Vector2.zero;
            Content.offsetMax = Vector2.zero;
            Content.localScale = Vector3.one;
            
            base.Setup();
        }

        public override void PlayHit()
        {
            _boxAnimator.SetTrigger(ShakeTile);
            base.PlayHit();
        }

        protected override void DestroyObject()
        {
            _boxAnimator.SetTrigger(DestroyTile);
            base.DestroyObject();
        }
        
        public override void PlayPreview()
        {
            Content.UpdateRectForTile(TileHelpMinOffset, TileHelpMaxOffset);
            Content.offsetMin = Vector2.zero;
            Content.offsetMax = Vector2.zero;
            Content.localScale = Vector3.one;
            base.PlayPreview();
        }
    }
}