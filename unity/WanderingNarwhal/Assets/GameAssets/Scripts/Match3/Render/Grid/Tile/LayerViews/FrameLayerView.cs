using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class FrameLayerView : SheetTileLayerViewBase
    {
        private GameObject[] _images;
        private ParticleSystem _particleSystem;
        private int _prevLevel = -1;
        private FrameLayerRenderer _renderer;

        public FrameLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<FrameLayerRenderer>();

            var childrenWithTag = new List<GameObject>();
            for (var i = 0; i < instance.transform.childCount; i++)
            {
                var child = instance.transform.GetChild(i).gameObject;
                if (child.CompareTag(SheetLayerTag))
                {
                    childrenWithTag.Add(child);
                }
            }
            _images = childrenWithTag.ToArray();

            _particleSystem = instance.GetComponentInChildren<ParticleSystem>();

            foreach (var image in _images)
            {
                image.SetActive(false);
            }
        }
        
        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);

            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    AudioProxy.PlaySound(Match3SoundIds.FrameDestroy);
                    FxRenderer.SpawnSingleAnimationEffect(coords, FxType.FrameRemoval);
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            for (int i = 0; i < _images.Length; i++)
                _images[i].gameObject.SetActive(i < newLevel);

            if (newLevel < _prevLevel)
            {
                if(_particleSystem != null)
                    _particleSystem.Play(false);
                AudioProxy.PlaySound(Match3SoundIds.FrameDestroy);
                if (coords.HasValue && newLevel > 0)
                {
                    FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.FrameLayerRemoval);
                }
                
                ShakeWithSiblings(() =>
                {
                    var animalRenderer = _renderer.transform.parent.GetComponentInChildren<AnimalLayerRenderer>();
                    if(animalRenderer == null) return;
                    animalRenderer.AdjustPositionForOneFrameAnimal();
                });
            }

            _prevLevel = newLevel;
        }
    }
}
