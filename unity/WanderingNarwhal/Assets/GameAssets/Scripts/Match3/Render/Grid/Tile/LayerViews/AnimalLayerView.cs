using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class AnimalLayerView : TileLayerViewBase, IPoolItem
    {
        private AnimalLayerRenderer _renderer;

        public AnimalLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponentInChildren<AnimalLayerRenderer>();
            _renderer.Init(tileResourceSelector.SelectedAnimalPrefab);
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim, animParams);

            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                case TileLayerViewAnims.Unapply:
                {
                    _renderer.AnimateHide();
                    break;
                }
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);

#if UNITY_EDITOR
            //reinit while switching in the editor
            if (BBB.M3Editor.M3Editor.IsCurrentSceneLevelEditorScene())
            {
                var selectedAnimalPrefab = tileResourceSelector.SelectedAnimalPrefab;
                if (_renderer.gameObject.name.Contains(selectedAnimalPrefab.name))
                    _renderer.Init(tileResourceSelector.SelectedAnimalPrefab);
            }
#endif

            _renderer.AnimateAppear();
        }

        public void OnInstantiate()
        {
        }

        public void OnSpawn()
        {
        }

        public void OnRelease()
        {
        }
    }
}