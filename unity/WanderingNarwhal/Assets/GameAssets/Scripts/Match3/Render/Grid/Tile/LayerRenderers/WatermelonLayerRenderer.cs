using BBB.Match3.Renderer;
using UnityEngine;

namespace BBB
{
    public class WatermelonLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private GameObject[] _images;

        public void ResetView()
        {
            foreach (var image in _images)
                image.SetActive(false);
            
            transform.localRotation = Quaternion.identity;
        }

        public void UpdateView(int count)
        {
            for (var i = 0; i < _images.Length; i++)
            {
                _images[i].SetActive(i < count);
            }
            
            transform.localRotation = Quaternion.identity;
        }
    }
}
