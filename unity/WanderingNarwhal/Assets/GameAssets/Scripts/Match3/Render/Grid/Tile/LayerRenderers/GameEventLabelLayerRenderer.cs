using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class GameEventLabelLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private GameEventIcon _icon;

        public void OnSpawn()
        {
            if (_icon == null)
            {
                _icon = GetComponentInChildren<GameEventIcon>();
            }

            _icon.Refresh();
            _icon.gameObject.SetActive(_icon.ImageRef.sprite != null);
        }
    }
}