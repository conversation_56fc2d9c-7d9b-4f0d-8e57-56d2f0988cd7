using BBB.Audio;
using BBB.Match3.Renderer;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB
{
    public class ColorCrateLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private SpriteRenderer[] _images = new SpriteRenderer[0];

        [SerializeField]
        private SpriteRenderer[] _imagesSecondary = new SpriteRenderer[0];

        [SerializeField]
        private GameObject[] _layers = new GameObject[0];

        [SerializeField]
        private string _takeHitSoundUid = Match3SoundIds.StickerDestroy;

        //[SerializeField]
        //private bool _playTapFeedback = true;

        public void ResetView()
        {
            foreach (var layer in _layers)
                layer.SetActive(false);
        }

        public void UpdateView(int count)
        {
            for (int i = 0; i < _layers.Length; i++)
            {
                _layers[i].SetActive(i < count);
            }
        }

        public void PlayFx()
        {
            AudioProxy.PlaySound(_takeHitSoundUid);
        }

        public void SetData(ColorCrateViewData data)
        {
            for (int i = 0; i < _images.Length; i++)
            {
                _images[i].color = data.Color;

                if (data.MainSprite != null)
                {
                    _images[i].sprite = data.MainSprite;
                }
            }

            for (int i = 0; i < _imagesSecondary.Length; i++)
            {
                _imagesSecondary[i].color = data.Color;
                if (data.SecondarySprite != null)
                {
                    _imagesSecondary[i].sprite = data.SecondarySprite;
                }
            }
        }
    }
}
