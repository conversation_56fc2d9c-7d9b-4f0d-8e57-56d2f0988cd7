using System;
using System.Collections;
using BBB.Audio;
using BBB.MMVibrations.Plugins;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class SandLayerRenderer : TileLayerRendererBase
    {
        [SerializeField]
        private GameObject _animatedRoot;

        [SerializeField]
        private SetMaterialPropertySlider _maskController;

        [SerializeField]
        private float _showAnimDuration = 0.9f;

        private Coroutine _appearAnim;
        private bool _autoPlayAppear = true;
        private Action _currentHideCallback;

        bool _wasShowCanceled;

        private void Awake()
        {
            _animatedRoot.SetActive(false);
            _maskController.enabled = false;

            _wasShowCanceled = false;
        }

        protected override void OnDisable()
        {
            _wasShowCanceled = true;

            if (_currentHideCallback != null)
            {
                _currentHideCallback();
                _currentHideCallback = null;
            }

            if (_appearAnim != null)
            {
                StopCoroutine(_appearAnim);
                _appearAnim = null;
                _wasShowCanceled = false;
            }

            _autoPlayAppear = true;
        }

        public void OnInstantiated(FxRenderer fxRenderer)
        {
            if (_autoPlayAppear)
            {
                PlaySandAppear(fxRenderer);
            }
        }

        [ContextMenu("Play Show")]
        public void PlaySandAppear(FxRenderer fxRenderer)
        {
            AudioProxy.PlaySound(Match3SoundIds.SandEnter);
            if (gameObject.activeInHierarchy)
            {
                _wasShowCanceled = false;
                bool runCheckReq = _appearAnim != null;
                _appearAnim = StartCoroutine(ShowAnim(runCheckReq, fxRenderer));
            }
            else
            {
                _animatedRoot.SetActive(true);
                _maskController.SliderValue = 1f;
                _maskController.ApplyMatProperty();
            }
        }

        [ContextMenu("Play Hide")]
        public void PlayDisappear(Action onDone)
        {
            // Note: Disappear is never called here.
            // Instead system spawns separated FX object, which plays disappear animation.
            AudioProxy.PlaySound(Match3SoundIds.SandExit);
            if (gameObject.activeInHierarchy)
            {
                _wasShowCanceled = false;
                _appearAnim = StartCoroutine(HideAnim());
                _currentHideCallback = onDone;
            }
            else
            {
                _animatedRoot.SetActive(false);
                _maskController.SliderValue = 0f;
                _maskController.ApplyMatProperty();
                onDone?.Invoke();
            }
        }

        private IEnumerator ShowAnim(bool needRunCheck, FxRenderer fxRenderer)
        {
            if (needRunCheck)
                while (_wasShowCanceled)
                    yield return null;

            _animatedRoot.SetActive(true);
            if (_showAnimDuration > 0)
            {
                var timer = 0f;
                while (timer <= _showAnimDuration)
                {
                    _maskController.SliderValue = timer / _showAnimDuration;
                    _maskController.ApplyMatProperty();
                    timer += Time.deltaTime;
                    if (_wasShowCanceled) break;
                    yield return null;
                }
            }

            _maskController.SliderValue = 1f; 
            _maskController.ApplyMatProperty();
            _appearAnim = null;
            _wasShowCanceled = false;

            fxRenderer.PlayHapticFeedback(ImpactPreset.LightImpact);
        }

        private IEnumerator HideAnim()
        {
            if (_showAnimDuration > 0)
            {
                var timer = 0f;
                while (timer <= _showAnimDuration)
                {
                    _maskController.SliderValue =  1f - timer / _showAnimDuration;
                    timer                       += Time.deltaTime;
                    _maskController.ApplyMatProperty();
                    yield return null;
                }
            }

            _maskController.SliderValue = 0f;
            _animatedRoot.SetActive(false);
            if (_currentHideCallback != null)
            {
                _currentHideCallback();
                _currentHideCallback = null;
            }
        }

        public override void PlayPreview()
        {
            if (_appearAnim != null)
            {
                StopCoroutine(_appearAnim);
                _appearAnim = null;
            }

            _autoPlayAppear = false;
            _animatedRoot.SetActive(true);
            _maskController.SliderValue = 1f;
            _maskController.ApplyMatProperty();
        }
    }
}