using BBB.DI;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public abstract class TileLayerBase : ITileLayer, IContextInitializable
    {
        protected TilesResources TilesResources;

        public abstract TileLayerState State { get; }

        public int CurrentOrder { get; private set; }

        public virtual void InitializeByContext(IContext context)
        {
            if (TilesResources == null)
            {
                TilesResources = context.Resolve<TilesResources>();
            }

            CurrentOrder = TilesResources.Get(State);
        }

        public bool IsRenderer(TileLayerState layerState)
        {
            return IsCondition(layerState);
        }

        public virtual void ConfigurateRenderer(Tile tile, RectTransform rendererTransform)
        {
            rendererTransform.localRotation = Quaternion.identity;
        }
        protected virtual bool IsCondition(TileLayerState state)
        {
            return (state & State) != 0;
        }
    }
}