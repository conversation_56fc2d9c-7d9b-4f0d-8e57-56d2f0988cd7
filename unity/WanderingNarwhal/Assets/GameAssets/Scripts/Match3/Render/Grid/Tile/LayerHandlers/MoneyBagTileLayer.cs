using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class MoneyBagTileLayer : TileLayerBase
    {
        public override TileLayerState State { get { return TileLayerState.MoneyBag; } }

        public override void ConfigurateRenderer(Tile tile, RectTransform rendererTransform)
        {
            base.ConfigurateRenderer(tile, rendererTransform);
            rendererTransform.sizeDelta = TilesResources.TileSize * 1.5f;
        }

        protected override bool IsCondition(TileLayerState state)
        {
            return (state & TileLayerState.MoneyBag) != 0 && (state & TileLayerState.Vase) == 0;
        }
    }
}