using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class MoleLayerView : SheetTileLayerViewBase
    {
        /// <summary>
        /// 0, 1, 2 - boy1 skin
        /// 3, 4, 5 - boy2 skin
        /// 6, 7, 8 - girl skin. 
        /// </summary>
        public int SkinIndex => _renderer != null ? _renderer.CurrentSkinIndex : 0;

        private MoleLayerRenderer _renderer;

        public MoleLayerView(ITileLayer layer) : base(layer)
        {
            
        }

        protected override void OnInstantiateViewUpdated(GameObject instance, Tile tile, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<MoleLayerRenderer>();
            _renderer.Init();
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            _renderer.SetLayer(newLevel);
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            if (_renderer != null)
            {
                _renderer.OnRefresh();
                _renderer.CurrentSkinIndex = coords.GetHashCode() % 9;
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            if (_renderer == null) return;

            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                {
                    IsPlayingLayerViewAnimation = true;
                    _renderer.PlayDestroy(onDone: OnDone);
                    break;

                    void OnDone()
                    {
                        IsPlayingLayerViewAnimation = false;
                    }
                }
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }
    }
}