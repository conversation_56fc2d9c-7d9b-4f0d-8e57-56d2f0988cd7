using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Debug;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class TileController : IContextInitializable, IContextReleasable
    {
        private readonly Dictionary<int, TileView> _tileViewsDict = new Dictionary<int, TileView>();
        private readonly List<TileView> _tileViews = new List<TileView>(121); //11x11
        private readonly Dictionary<int, TileView> _destroyedViewsDict = new Dictionary<int, TileView>();

        private static readonly List<int> IdsToDestroy = new List<int>(100);
        private static readonly List<int> IdsExist = new List<int>(100);

        private IContext _context;
        private ICoroutineExecutor _coroutineExecutor;
        private GameController _gameController;
        private TileRevealer _tileRevealer;

        private static Comparer<TileView> _posComparer = Comparer<TileView>.Create(CompareTileViewsByPos);

        public void InitializeByContext(IContext context)
        {
            _tileViewsDict.Clear();
            _tileViews.Clear();
            _destroyedViewsDict.Clear();
            _context = context;
            _coroutineExecutor = context.Resolve<ICoroutineExecutor>();
            _gameController = context.Resolve<GameController>();
            _tileRevealer = context.Resolve<TileRevealer>();
        }

        public void CreateTileViewsInstant(Grid grid)
        {
            for (int i = grid.Cells.Count - 1; i >= 0; i--)
            {
                var cell = grid.Cells[i];
                if (cell.Tile.IsNull()) continue;

                var tileView = GetOrCreateTileView(cell.Tile, cell);
                tileView.TileMotionController.SetPosAndFreeze(cell.Coords.ToUnityVector2());
                tileView.transform.SetSiblingIndex(cell.Coords.Y*10+cell.Coords.X);
                tileView.CustomAnimatedAppear();
            }
        }

        public void CreateAndRevealTileViews(Grid grid, int revealMoveChange)
        {
            for (int i = grid.Cells.Count - 1; i >= 0; i--)
            {
                var cell = grid.Cells[i];
                if (cell.Tile.IsNull()) continue;

                var tileView = GetOrCreateTileView(cell.Tile, cell);
                tileView.transform.SetSiblingIndex(cell.Coords.Y * 10 + cell.Coords.X);
            }

            _tileRevealer.Reveal(_tileViewsDict, grid, revealMoveChange);
        }

        public void ForceFinishReveal()
        {
            _tileRevealer.ForceFinishReveal();
        }

        public void Update(Grid grid, bool force = false)
        {
            foreach (var cell in grid.Cells)
            {
                var tile = cell.Tile;
                TileView view;
                if (!ReferenceEquals(tile, null) && _tileViewsDict.TryGetValue(tile.Id, out view))
                {
                    view.UpdateView(cell, tile, cell.Coords, force);
                }
            }
            DestroyMissedTiles(grid);
        }

        public void ForceUpdateTileView(Grid grid, TileView tileView)
        {
            foreach (var cell in grid.Cells)
            {
                var tile = cell.Tile;
                TileView view;
                if (!ReferenceEquals(tile, null) && _tileViewsDict.TryGetValue(tile.Id, out view))
                {
                    if (view == tileView)
                    {
                        view.UpdateView(cell, tile, cell.Coords, force: true);
                        return;
                    }
                }
            }
        }

        public void ForceUpdateCurrentState(Cell cell, TileView tileView)
        {
            if (ReferenceEquals(cell.Tile, null) || !_tileViewsDict.TryGetValue(cell.Tile.Id, out var view)) return;
            if (view == tileView)
            {
                view.UpdateView(cell, cell.Tile, cell.Coords, true);
            }
        }

        public TileView GetOrCreateTileView(Tile tile, Cell cell = null)
        {
            if (ReferenceEquals(tile, null))
            {
#if BBB_LOG
                M3Debug.LogError("[GridController] Tile is null");
#endif

                return null;
            }

            TileView tileView;
            if (!_tileViewsDict.TryGetValue(tile.Id, out tileView))
            {
                tileView = SpawnTileView();
                _tileViewsDict[tile.Id] = tileView;
                _tileViews.Add(tileView);
                tileView.UpdateView(cell, tile, cell?.Coords ?? Coords.OutOfGrid, isReveal: true);
            }

            return tileView;
        }

        public TileView GetExistingTileView(int tileId)
        {
            _tileViewsDict.TryGetValue(tileId, out var tileView);
            return tileView;
        }

        /// <summary>
        /// Create tile view that must be displayed outside grid.
        /// </summary>
        public TileView CreateTileViewPreviewForTile(Tile tile)
        {
            var result = SpawnTileView();
            result.UpdateView(null, tile, Coords.OutOfGrid);
            return result;
        }

        public bool IsTileViewDestroyed(int id)
        {
            return _destroyedViewsDict.ContainsKey(id);
        }

        public TileView GetDestroyedTileViewById(int id)
        {
            if (_destroyedViewsDict.ContainsKey(id))
            {
                return _destroyedViewsDict[id];
            }

            return null;
        }

        private TileView SpawnTileView()
        {
            var rendererContainers = _context.Resolve<RendererContainers>();
            var pool = rendererContainers.TilesViewPool;
            var spawn = pool.Spawn();
            var tileView = spawn.GetComponent<TileView>();
            var rectTransform = tileView.GetComponent<RectTransform>();
            rectTransform.sizeDelta = _context.Resolve<TilesResources>().TileSize;
            tileView.Init(_context);
            return tileView;
        }

        public IEnumerable<TileView> GetAllTileViews()
        {
            return _tileViewsDict.Values;
        }

        public IReadOnlyList<TileView> GetAllTileViewsOrdered()
        {
            _tileViews.TimSort(_posComparer);
            return _tileViews;
        }

        private static int CompareTileViewsByPos(TileView lhs, TileView rhs)
        {
            int yDiff = lhs.TileMotionController.TargetPosition.y.CompareTo(rhs.TileMotionController.TargetPosition.y);
            return yDiff == 0 ? lhs.TileMotionController.TargetPosition.x.CompareTo(rhs.TileMotionController.TargetPosition.x) : yDiff;
        }

        public TileView GetTileViewById(int id, bool allowLogError = true)
        {
            if (_tileViewsDict.TryGetValue(id, out var tileView)) return tileView;
            
            if (allowLogError)
            {
                BDebug.LogError(LogCat.Match3, $"TileView not found by id {id} StackTrace: {StackTraceUtility.ExtractStackTrace()}");
            }

            return null;
        }

        public bool HasTileViewInCoord(Coords coords)
        {
            return _gameController.Grid.TryGetCell(coords, out var cell) && !ReferenceEquals(cell.Tile, null) && _tileViewsDict.ContainsKey(cell.Tile.Id);
        }

        public TileView GetTileViewByCoord(Coords coords, bool allowLogError = true)
        {
            return _gameController.Level != null && _gameController.Level.Grid.TryGetCell(coords, out var cell) && !ReferenceEquals(cell.Tile, null) ? GetTileViewById(cell.Tile.Id, allowLogError) : null;
        }

        public void ReleaseTileWhenCan(int tileId, Coords vibrationCoord)
        {
            if (!_tileViewsDict.Remove(tileId, out var tileView)) return;
            
            _tileViews.TimSort(_posComparer);
            int index = _tileViews.BinarySearchReference(tileView, _posComparer);
            _tileViews.RemoveAt(index);
            _destroyedViewsDict[tileId] = tileView;
            tileView.StartReleaseWhenCan(_coroutineExecutor, vibrationCoord);
        }

        public void VisualizeTileDestruction(Cell cell,
            TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None,
            PreDestroyTweener preDestroyTweener = default)
        {
            if (cell.HasTile() && _tileViewsDict.TryGetValue(cell.Tile.Id, out var tileView))
            {
                tileView.StartAnimateDestroy(cell.Coords, animParams, _coroutineExecutor, preDestroyTweener);
            }
        }

        public TileView UpdateCount(Cell cell)
        {
            var tileView = GetOrCreateTileView(cell.Tile);
            if(tileView != null)
                tileView.UpdateCounters(cell);
            return tileView;
        }

        private void DestroyMissedTiles(Grid grid)
        {
            foreach (var cell in grid.Cells)
            {
                if (!cell.Tile.IsNull())
                {
                    IdsExist.Add(cell.Tile.Id);
                }
            }

            foreach (var kv in _tileViewsDict)
            {
                if (!IdsExist.Contains(kv.Key))
                {
                    if (!kv.Value.IsPendingTileCreation)
                    {
                        IdsToDestroy.Add(kv.Key);
                    }
                }
                else
                {
                    kv.Value.IsPendingTileCreation = false;
                }
            }

            _tileViews.TimSort(_posComparer);
            foreach (var id in IdsToDestroy)
            {
                int index = _tileViews.BinarySearchReference(_tileViewsDict[id], _posComparer);
                _tileViews.RemoveAt(index);
                _tileViewsDict[id].ReleaseItem();
                _tileViewsDict.Remove(id);
            }

            IdsExist.Clear();
            IdsToDestroy.Clear();
        }

        public void Clear()
        {
            foreach (var tileView in _tileViewsDict.Values)
            {
                if (tileView == null) continue;

                tileView.gameObject.Release();
            }
            _tileViewsDict.Clear();
            _tileViews.Clear();
            _destroyedViewsDict.Clear();

        }

        public void ReleaseByContext(IContext context)
        {
            _context = null;
            Clear();
        }
    }
}
