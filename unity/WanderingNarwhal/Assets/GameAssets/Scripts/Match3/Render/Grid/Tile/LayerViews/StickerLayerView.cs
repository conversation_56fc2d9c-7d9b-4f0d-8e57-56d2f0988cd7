using System.Collections.Generic;
using BBB.Audio;
using BebopBee.Core.Audio;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class StickerLayerView : SheetTileLayerViewBase
    {
        private StickerLayerRenderer _renderer;
        private StickerRenderVariant _currentVariant;
        private int _prevLevel = -1;
        
        public StickerLayerView(ITileLayer layer) : base(layer)
        {}

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            _currentVariant = tileResourceSelector.SelectedStickerVariant;
            _renderer.Setup(_currentVariant);
        }

        protected override void OnInstantiateView(GameObject instance, ITileLayer layer, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<StickerLayerRenderer>();
            if (_renderer == null)
            {
                UnityEngine.Debug.LogError($"Missing component {typeof(StickerLayerRenderer).Name} on tile prefab: {instance.name}", instance);
            }
            else
            {
                _renderer.ResetView();
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams)
        {
            base.Animate(coords, anim);
            
            switch (anim)
            {
                case TileLayerViewAnims.Destroy:
                    AudioProxy.PlaySound(Match3SoundIds.StickerDestroy);
                    FxRenderer.SpawnSingleAnimationEffectWithCustomParameters(coords, FxType.StickerRemove, new FxOptionalParameters() { skin = (int)_currentVariant.Skin }, releaseTime: 3);
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;
            }
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            _renderer.UpdateView(newLevel);
            if (newLevel < _prevLevel)
            {
                _renderer.PlayFx();
                ShakeWithSiblings();
                if (coords.HasValue)
                    switch (newLevel)
                    {
                        case 2:
                        {
                            FxRenderer.SpawnSingleAnimationEffectWithCustomParameters(coords.Value, FxType.StickerThirdLayerRemove, new FxOptionalParameters() { skin = (int)_currentVariant.Skin }, releaseTime: 3);
                            break;
                        }
                        case 1:
                        {
                            FxRenderer.SpawnSingleAnimationEffectWithCustomParameters(coords.Value, FxType.StickerSecondLayerRemove, new FxOptionalParameters() { skin = (int)_currentVariant.Skin }, releaseTime: 3);
                            break;
                        }
                    }
            }
            _prevLevel = newLevel;
        }
    }
}