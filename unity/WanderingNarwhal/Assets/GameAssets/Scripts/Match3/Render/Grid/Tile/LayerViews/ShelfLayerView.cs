using System.Collections.Generic;
using BBB.Audio;
using BBB.MMVibrations.Plugins;
using BebopBee.Core.Audio;
using UnityEngine;

namespace BBB.Match3.Renderer
{
    public class ShelfLayerView : SheetTileLayerViewBase, IAlwaysApplicableTileView
    {
        private ShelfLayerRenderer _renderer;
        private int _prevLevel = -1;

        public ShelfLayerView(ITileLayer layer) : base(layer)
        {
        }

        protected override void OnInstantiateViewUpdated(GameObject instance, Tile tile, Vector2 cellSize)
        {
            _renderer = instance.GetComponent<ShelfLayerRenderer>();
            _renderer.Init();
        }

        public override void Apply(Tile tile, Coords coords, Transform container, IEnumerable<ITileLayerView> viewsList,
            bool isVisible)
        {
            base.Apply(tile, coords, container, viewsList, isVisible);
            if (coords != Coords.OutOfGrid)
            {
                var shelfGroupIdentifier = tile.GetParam(TileParamEnum.ShelfGroupIdentifier);
                _renderer.UpdateShelf(GridController, coords, shelfGroupIdentifier);
                _renderer.OnRefresh();
            }
        }

        public void PlayShake()
        {
            _renderer.ShakeItem();
        }

        protected override int SelectSheetsCountValue(Tile tile)
        {
            return tile.GetParam(TileParamEnum.AdjacentHp);
        }

        protected override void ChangeSheetLevel(int newLevel, Tile tile, Coords? coords = null)
        {
            if (_prevLevel != newLevel && coords.HasValue)
            {
                if (newLevel == 1)
                {
                    _renderer.DestroyShelfItem();
                    AudioProxy.PlaySound(Match3SoundIds.ShelfItemBreak);
                    FxRenderer.PlayHapticFeedback(ImpactPreset.MediumImpact);
                    FxRenderer.SpawnSingleAnimatorEffect(coords.Value, FxType.ShelfItemDestroy, _renderer.ShelfItemDestroyTime);
                }

                _prevLevel = newLevel;
            }
        }

        public override void Animate(Coords coords, TileLayerViewAnims anim,
            TileLayerViewAnimParams animParams = TileLayerViewAnimParams.None)
        {
            base.Animate(coords, anim, animParams);
            switch (anim)
            {
                case TileLayerViewAnims.CustomAppear:
                    _renderer.PlayAppear();
                    break;

                case TileLayerViewAnims.Preview:
                    _renderer.PlayPreview();
                    break;

                case TileLayerViewAnims.Destroy:
                    IsPlayingLayerViewAnimation = true;
                    AudioProxy.PlaySound(Match3SoundIds.ShelfDestroy);
                    FxRenderer.SpawnSingleAnimatorEffect(coords, FxType.ShelfTileDestroy, _renderer.ShelfDestroyTime);
                    IsPlayingLayerViewAnimation = false;
                    break;
                case TileLayerViewAnims.TapFeedback:
                    _renderer.PlayTapFeedback(this);
                    break;
            }
        }
    }
}