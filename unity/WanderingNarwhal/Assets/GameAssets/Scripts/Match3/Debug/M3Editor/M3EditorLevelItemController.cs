#if UNITY_EDITOR
using System.Collections.Generic;
using System.Linq;
using BBB.UI;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

// ReSharper disable once CheckNamespace
namespace BBB.M3Editor
{
    // ReSharper disable once UnusedMember.Global
    public sealed class M3EditorLevelItemController : M3EditorBase
    {
        private const string _toggleDebugInfoName = "DebugInfo";
        private const string _toggleBackgroundName = "ShowBackground";
        private const string _toggleDesignersModeName = "DesignersMode";
        private const string _toggleSpawnersModeName = "SpawnersMode";
        private const string _toggleArmoredTilesModeName = "ArmoredTiles";
        private M3EditorDebugView _gridDebugView;
        private GameObject _levelDebugView;
        private GameObject _levelFileName;
        private GameObject _levelBackground;

        private Dictionary<string, Toggle> _togglesByName = new();

        public M3EditorLevelItemController(M3Editor m3Editor) : base(m3Editor)
        {
        }

        public override void SetUpUi()
        {
            base.SetUpUi();
            SetToggleActions();
            UpdateUiRefs();
        }

        private void UpdateUiRefs()
        {
            _gridDebugView = _m3Editor.GameController.GridController.Transform.GetComponentInChildren<M3EditorDebugView>();
    
            var debugMenu = _m3Editor.transform.parent.GetComponentInChildren<LevelControllerDebugMenu>();
            if (debugMenu != null)
            {
                debugMenu.gameObject.SetActive(false);
                _levelDebugView = debugMenu.transform.parent.gameObject;
            }
    
            _levelFileName = GameObject.Find("LevelName");
            _levelBackground = GameObject.Find("BackgroundImage");
        }

        public override void UpdateUi()
        {
            base.UpdateUi();
            UpdateUiRefs();
            SetDebugInfo(_togglesByName[_toggleDebugInfoName].isOn);
            SetLevelBackground(_togglesByName[_toggleBackgroundName].isOn);
            SetDesignersMode(_togglesByName[_toggleDesignersModeName].isOn);
            SetSpawnersMode(_togglesByName[_toggleSpawnersModeName].isOn);
            SetArmoredTilesMode(_togglesByName[_toggleArmoredTilesModeName].isOn);
        }

        private void SetToggleActions()
        {
            var toggleActions = new Dictionary<string, UnityAction<bool>>
            {
                {_toggleDebugInfoName, SetDebugInfo},
                {_toggleBackgroundName, SetLevelBackground},
                {_toggleDesignersModeName, SetDesignersMode},
                {_toggleSpawnersModeName, SetSpawnersMode},
                {_toggleArmoredTilesModeName, SetArmoredTilesMode},
            };
            var allToggles = _m3Editor.GetComponentsInChildren<Toggle>(false);
            _togglesByName.Clear();
            foreach (var toggle in allToggles)
            {
                if (toggleActions.TryGetValue(toggle.name, out var action))
                {
                    toggle.onValueChanged.RemoveAllListeners();
                    toggle.onValueChanged.AddListener(action);
                    _togglesByName.Add(toggle.name, toggle);
                }
            }

            if (_togglesByName.Count < toggleActions.Count)
            {
                foreach (var kvp in toggleActions)
                {
                    if (_togglesByName.ContainsKey(kvp.Key)) continue;
                    Debug.LogError($"Cannot find action for Toggle '{kvp.Key}'");
                }
            }
        }

        private void SetDebugInfo(bool active)
        {
            if (_levelFileName != null) _levelFileName.SetActive(active);
            if (_gridDebugView != null) _gridDebugView.gameObject.SetActive(active);
            if (_levelDebugView != null) _levelDebugView.SetActive(active);
        }

        private void SetLevelBackground(bool active)
        {
            if (_levelBackground != null) _levelBackground.SetActive(active);
        }

        private void SetDesignersMode(bool active)
        {
            _gridDebugView.DesignersMode = active;
        }

        private void SetSpawnersMode(bool active)
        {
            _gridDebugView.SpawnersMode = active;
        }

        private void SetArmoredTilesMode(bool active)
        {
            _gridDebugView.ArmoredTiles = active;
        }

        public void SetArmoredTilesToggleOn()
        {
            _togglesByName[_toggleArmoredTilesModeName].isOn = true;
        }
    }
}
#endif
