#if UNITY_EDITOR
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.DI;
using BBB.DI.Extensions;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.UI;
using BBB.UI.Level.Controllers;
using Bebopbee.Core.Extensions.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorDebugView : BbbMonoBehaviour, IContextInitializable, IContextReleasable
    {
        [SerializeField] private TextMeshProUGUI _winLostText;
        [SerializeField] private TextMeshProUGUI _inputLocked;
        [SerializeField] private Button _addMovesButton;

        private RectTransform _cachedTransform;

        private readonly Dictionary<Coords, List<GameObject>> _debugIcons = new ContextedDictionary<Coords, List<GameObject>>();

        private IGridController _gridController;
        private ICellController _cellController;
        private IEventDispatcher _eventDispatcher;
        private GameController _gameController;
        private Match3EditorResourceProvider _resourceProvider;

        private Grid _cachedGrid;
        private bool _designersMode;
        private bool _spawnerMode = true;
        private bool _armoredTiles = true;

        public bool DesignersMode
        {
            get => _designersMode;
            set
            {
                if (_designersMode != value)
                {
                    _designersMode = value;
                    Refresh();
                }
            }
        }

        public bool SpawnersMode
        {
            get => _spawnerMode;
            set
            {
                if (_spawnerMode != value)
                {
                    _spawnerMode = value;
                    Refresh();
                }
            }
        }
        public bool ArmoredTiles
        {
            get => _armoredTiles;
            set
            {
                if (_armoredTiles != value)
                {
                    _armoredTiles = value;
                    Refresh();
                }
            }
        }


        public void InitializeByContext(IContext context)
        {
            _winLostText.gameObject.SetActive(false);
            var rectTransform = gameObject.GetOrAddComponent<RectTransform>();
            rectTransform.pivot = Vector2.zero;
            rectTransform.anchorMax = new Vector2(1f, 1f);
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.offsetMin = rectTransform.offsetMax = new Vector2(0, 0);

            _cachedTransform = rectTransform;

            _cellController = context.Resolve<ICellController>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _gridController = context.Resolve<IGridController>();
            _gameController = context.Resolve<GameController>();
            _resourceProvider = context.Resolve<Match3EditorResourceProvider>();
            _inputLocked.gameObject.SetActive(_gameController.IsInputLocked);

            Subscribe();

            _addMovesButton.ReplaceOnClick(() => { AddMoves(false, 5); });
        }

        public void AddMoves(bool replaySystem, int quantity)
        {
            if (replaySystem)
            {
                AssistParams.AimAtWinningLevel = true;
            }

            _gameController.AddExtraMoves(quantity);
            _winLostText.gameObject.SetActive(false);
            _addMovesButton.gameObject.SetActive(false);

            var moreMovesAddedEvent = _eventDispatcher.GetMessage<MoreMovesAddedEvent>();
            moreMovesAddedEvent.Set(quantity, null, default, false);
            _eventDispatcher.TriggerEvent(moreMovesAddedEvent);
        }

        private void OnInputLocked(bool inputLocked)
        {
            if (_inputLocked != null)
            {
                _inputLocked.gameObject.SetActive(inputLocked);
            }
        }

        public void ReleaseByContext(IContext context)
        {
            Clear();
            Unsubscribe();
            _eventDispatcher = null;
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            Unsubscribe();
            _eventDispatcher = null;
        }

        private void Subscribe()
        {
            Unsubscribe();

            _eventDispatcher.AddListener<LevelEndedEvent>(OnLevelEnded);
            _gameController.InputLocked += OnInputLocked;
        }

        private void Unsubscribe()
        {
            if (_eventDispatcher != null)
            {
                _eventDispatcher.RemoveListener<LevelEndedEvent>(OnLevelEnded);
            }

            if (_gameController != null)
            {
                _gameController.InputLocked -= OnInputLocked;
            }
        }


        public void HideText()
        {
            _winLostText.gameObject.SetActive(false);
        }

        public void Refresh(Grid grid = null)
        {
            if (grid == null)
                grid = _cachedGrid;
            else
                _cachedGrid = grid;

            //  Draw debug axis numbers
            Clear();
            var cellIndexBase = _designersMode ? 1 : 0;
            for (var y = 0; y < grid.Height; ++y)
            {
                var icon =
                    GetOrCreateIcon(new Coords(-1, y), EditorPrefabNames.DebugViewCoord);
                var renderer = icon.GetComponent<DebugViewCoordRenderer>();
                var ay = y + cellIndexBase;
                renderer.Init(ay, ay >= 10
                    ? HorizontalAlignmentOptions.Left
                    : HorizontalAlignmentOptions.Center);
            }

            for (var x = 0; x < grid.Width; ++x)
            {
                var icon =
                    GetOrCreateIcon(new Coords(x, grid.Height), EditorPrefabNames.DebugViewCoord);
                var renderer = icon.GetComponent<DebugViewCoordRenderer>();
                renderer.Init(x + cellIndexBase);
            }

            foreach (var cell in grid.Cells)
            {
                // draw spawners/despawners
                if (_spawnerMode)
                {
                    if (cell.IsAnyOf(CellState.Spawner))
                    {
                        var icon = GetOrCreateIcon(cell.Coords, EditorPrefabNames.DebugViewSpawner);
                        var renderer = icon.GetComponent<DebugSpawnerRenderer>();
                        if (renderer != null)
                        {
                            renderer.SetSpawnerView(cell.SpawnerUid);
                        }
                    }

                    if (cell.IsAnyOf(CellState.Despawner))
                    {
                        GetOrCreateIcon(cell.Coords, EditorPrefabNames.DebugViewDespawner);
                    }
                }

                if (cell.IsAnyOf(CellState.NotShuffable))
                {
                    GetOrCreateIcon(cell.Coords, EditorPrefabNames.DebugViewNotShuffable);
                }

                if (cell.IsAnyOf(CellState.InvisibleWall))
                {
                    var go = GetOrCreateIcon(cell.Coords, EditorPrefabNames.DebugViewInvisibleWalls);
                    AdjustWallOrientation(go, cell);
                }
            }

            RefreshArmoredTiles();
        }

        private void RefreshArmoredTiles()
        {
            foreach (var cell in _cachedGrid.Cells)
            {
                if (cell.IsAnyOf(CellState.Ivy | CellState.Tnt))
                {
                    var cellView = _cellController.GetCellView(cell.Coords);
                    var view = cellView.GetView(CellLayerState.Ivy | CellLayerState.Tnt);
                    if (_armoredTiles)
                        view.Reveal(0.0f);
                    else
                        view.Hide();
                }
            }
        }


        private void AdjustWallOrientation(GameObject go, Cell cell)
        {
            if (cell.InvisibleWalls == null) return;
            var renderer = go.GetComponent<DebugInvisibleWallRenderer>();
            renderer.UpdateWalls(cell.InvisibleWalls.Directions);
        }

        private void Clear()
        {
            //_winLostText.gameObject.SetActive(false);

            foreach (var list in _debugIcons.Values)
            {
                if (list == null)
                    continue;

                foreach (var go in list)
                    Destroy(go);
            }

            _debugIcons.Clear();
        }

        private GameObject GetOrCreateIcon(Coords coords, string prefabName)
        {
            if (!_debugIcons.ContainsKey(coords))
                _debugIcons.Add(coords, new List<GameObject>());

            var prefab = _resourceProvider.GetEditorPrefab(prefabName);
            var go = Instantiate(prefab, _cachedTransform);
            go.transform.localPosition = _gridController.ToLocalPosition(coords);
            _debugIcons[coords].Add(go);
            return go;
        }

        private void OnLevelEnded(LevelEndedEvent ev)
        {
            _winLostText.gameObject.SetActive(true);
            _winLostText.text = ev.LevelOutcome == LevelOutcome.Win ? "You win" : "You lose";
            _addMovesButton.gameObject.SetActive(ev.LevelOutcome != LevelOutcome.Win);
        }
    }
}
#endif