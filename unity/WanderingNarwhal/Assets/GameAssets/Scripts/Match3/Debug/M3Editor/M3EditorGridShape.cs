#if UNITY_EDITOR
using BBB.CellTypes;
using UnityEngine.UI;

// ReSharper disable once CheckNamespace
namespace BBB.M3Editor
{
    // ReSharper disable once UnusedMember.Global
    public sealed class M3EditorGridShape : M3EditorBase
    {
        private InputField _inputWidth;
        private InputField _inputHeight;

        public M3EditorGridShape(M3Editor m3Editor) : base(m3Editor) { }

        public override void SetUpUi()
        {
            var children = _m3Editor.GetComponentsInChildren<InputField>(true);
            foreach (var child in children)
            {
                if (child.name == "WidthInputField")
                {
                    _inputWidth = child;
                }

                if (child.name == "HeightInputField")
                {
                    _inputHeight = child;
                }
            }

            foreach (var child in _m3Editor.GetComponentsInChildren<Button>(true))
            {
                if (child.name == "SetGridSize")
                {
                    child.onClick.AddListener(SetGridSize);
                }
            }
        }

        public override void UpdateUi()
        {
            _inputWidth.text = Grid.Width.ToString();
            _inputHeight.text = Grid.Height.ToString();
        }

        private void SetGridSize()
        {
            if (!int.TryParse(_inputHeight.text, out var height)) return;
            if (!int.TryParse(_inputWidth.text, out var width)) return;

            var newGrid = new Grid(width, height);
            for (var i = 0; i < height; i++)
            {
                for (var j = 0; j < width; j++)
                {
                    newGrid.CreateCell(new Coords(j, i));
                }
            }

            for (var i = 0; i < width; i++)
            {
                if (newGrid.TryGetCell(new Coords(i, height - 1), out var cell))
                {
                    cell.Add(CellState.Spawner);
                }
            }

            _m3Editor.HardRestartWith(string.Empty, newGrid);
        }
    }
}

#endif
