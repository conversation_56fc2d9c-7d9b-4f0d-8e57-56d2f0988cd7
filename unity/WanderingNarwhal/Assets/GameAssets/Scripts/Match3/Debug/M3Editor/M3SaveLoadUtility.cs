using System;
using System.IO;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3;
using UnityEngine;

namespace BBB.M3Editor
{
    public static class M3SaveLoadUtility
    {
        public const string LevelsFolder = "Assets/Levels/";

        private static string _lastLoadedFile;

        public static string LastEditedLevelPath
        {
            get => PlayerPrefs.GetString("LastEditedM3Level", string.Empty);
            set => PlayerPrefs.SetString("LastEditedM3Level", value);
        }

        public static string CurrentLevelUid { get; set; }
        
        public static string SaveLevelToLastLoadedFile(ILevel level)
        {
            SaveLevelIntoFile(level, _lastLoadedFile, updateCurrentLevelPath: true);
            return _lastLoadedFile;
        }
        
        public static void SaveLevelIntoFile(ILevel level, string pathAndFileName, bool updateCurrentLevelPath = false)
        {
            var data = LevelBinarySerializer.Serialize(level);

            var fileName = Path.GetFileName(pathAndFileName);

            if (fileName == null)
                return;

            using var stream = new FileStream(pathAndFileName, FileMode.Create);
            using var writer = new BinaryWriter(stream);
            writer.Write(data);
            writer.Close();
            if (updateCurrentLevelPath)
            {
                LastEditedLevelPath = pathAndFileName;
            }
        }

        public static string GetShortPath(string fullPath)
        {
            var workingStr = fullPath.Replace("\\", "/");
            var index = workingStr.IndexOf(LevelsFolder, StringComparison.InvariantCulture);

            if (index != -1)
            {
                return workingStr.Substring(index, workingStr.Length - index);
            }
            Debug.LogError("/Assets/Levels is not found in level path. Full path=" + fullPath);
            return fullPath;

        }

        public static string LoadLevelFromFile(string path, out Level level, bool allowSaveAsLastLevel = true)
        {
            var fileName = Path.GetFileName(path);
            if (fileName == null)
            {
                level = null;
                return null;
            }

            if (!File.Exists(path))
            {
                Debug.LogErrorFormat("File {0} does not exist", path);
                level = null;
                return null;
            }

            var data = File.ReadAllBytes(path);

            if (data == null)
                throw new Exception("Null data exception");

            level = LevelBinarySerializer.Deserialize(data);
            if (!allowSaveAsLastLevel) return path;
            _lastLoadedFile = path;
            LastEditedLevelPath = path;

            return path;
        }
    }
}
