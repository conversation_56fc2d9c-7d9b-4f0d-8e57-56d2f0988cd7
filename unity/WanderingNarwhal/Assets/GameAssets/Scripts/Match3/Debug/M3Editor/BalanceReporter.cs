#if UNITY_EDITOR
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using BBB.DI;
using BBB.Map;
using BBB.Match3.Systems;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.M3Editor
{
    public class BalanceReportStarted : IEvent
    {
        
    }
    
    public class BalanceReporter : IContextInitializable
    {
        private IEventDispatcher _eventDispatcher;
        private GameController _gameController;
        private M3EditorProgressPopup _progressPopup;
        private AutoBruteSystem _autoBruteSystem;
        private IDictionary<string, FBConfig.ProgressionLevelConfig> _levelConfigs;
        private SpawnerSettingsManager _spawnerSettingsManager;

        public void InitializeByContext(IContext context)
        {
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _gameController = context.Resolve<GameController>();
            _progressPopup = context.Resolve<M3EditorProgressPopup>();
            _autoBruteSystem = context.Resolve<AutoBruteSystem>();

            var configProvider = context.Resolve<IConfig>();
            _levelConfigs = configProvider.Get<FBConfig.ProgressionLevelConfig>();
            _spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
        }

        public void GenerateBalanceReportFor()
        {
            _gameController.StartCoroutineMethod(ReportRoutine());
        }

        private string GenerateHeader(BalanceReportConfigObject reportConfig)
        {
            var headerBuilder = new StringBuilder();
            headerBuilder.Append("LevelUid\t");
            headerBuilder.Append($"EasyTileKindGoals (>{(1f-reportConfig.EasyThreshold)*100}% reaches)\t");
            headerBuilder.Append($"MediumTileKindGoals (>{(1f-reportConfig.MediumThreshold)*100}% reaches)\t");
            headerBuilder.Append($"HardTileKindGoals (>{(1f-reportConfig.HardThreshold)*100}% reaches)\t");
            headerBuilder.Append("ScoreAndTileResults\t");
            headerBuilder.Append("GridBasedResults\t");
            headerBuilder.Append("WinRatio\t");
            headerBuilder.Append("LossReasons\t");
            headerBuilder.Append("RemainingMovesOnWinsAvg\t");
            headerBuilder.Append("SuperBoostsUsedAvg\t");
            headerBuilder.Append("LineBreakersCreatedAvg\t");
            headerBuilder.Append("BombsCreatedAvg\t");
            headerBuilder.Append("BoltsCreatedAvg\t");
            headerBuilder.Append("PropellersCreatedAvg\t");
            headerBuilder.Append("Shuffles\t");
            headerBuilder.Append("EmptyMovesAvg\t");
            headerBuilder.Append("MovesMin\t");
            headerBuilder.Append("MovesAvg\t");
            headerBuilder.Append("MovesMed\t");
            return headerBuilder.ToString();
        }
        
        private IEnumerator ReportRoutine()
        {
            _eventDispatcher.TriggerEvent(_eventDispatcher.GetMessage<BalanceReportStarted>());
            var reportConfig = AiDataManagement.LoadBalacingReportConfig();
            var shouldBreak = false;
            var shouldSkip = new [] { false };
            _progressPopup.SetCancelCallback(() => { shouldBreak = true; });
            _progressPopup.SetSkipCallback(() => { shouldSkip[0] = true; });
            _progressPopup.SetProcessTitle("Gathering report");

            var outputPath = Path.Combine(Application.dataPath, "GameAssets/_Sandbox/BalanceReport.txt");

            var writer = new StreamWriter(outputPath);
            var header = GenerateHeader(reportConfig);
            writer.WriteLine(header);

            var levelConfigs = reportConfig.GetLevelsToUse(_levelConfigs).Select(uid => _levelConfigs[uid]).ToList();
            
            var index = 0;
            foreach (var levelConfig in levelConfigs)
            {
                writer.Write(levelConfig.Uid + "\t");
                
                if (shouldBreak)
                    break;

                _progressPopup.ShowFirstBar(string.Format($"Reporting on {levelConfig.Uid}"), index / (float) levelConfigs.Count);

                yield return null;

                var level = levelConfig.LoadLevel((int)reportConfig.Stage, _spawnerSettingsManager.SpawnerSettings);
                
                if (level == null)
                    continue;
                    
                var autoBruteParams = new AutoBruteParams
                {
                    Limit = reportConfig.Limit,
                    PickLogic = reportConfig.PickLogic,
                    Runs = reportConfig.Runs,
                    LimitNumber = level.TurnsLimit,
                    TestName = level.Config.Uid,
                    ShowBruteProgress = true,
                    UseSuperBoost = true
                };

                shouldSkip[0] = false;
                yield return _autoBruteSystem.GatherStatistics(level, autoBruteParams, shouldSkip, 
                    (stats, code, message) =>
                    {
                        if (code == AutoBruteErrorCode.Exception)
                        {
                            writer.WriteLine($"Exception occured: \n {message}");
                        }
                        else
                        {
                            var goalsDict = level.Goals.ToNameDict();
                            var reportString = ConvertStatsToReportString(reportConfig, stats, goalsDict, code);
                            writer.WriteLine(reportString);
                        }
                        
                    });
                    
                index++;

            }
            writer.Close();
            _progressPopup.Hide();
        }

        public static string ConvertStatsToReportString(BalanceReportConfigObject reportConfig, Match3Statistics stats, IDictionary<string, int> goalDict, AutoBruteErrorCode errorCode)
        {
            var stringBuilder = new StringBuilder();

            var tileKindResults = stats.ResultTypes.Where(resultType =>
            {
                if (Enum.TryParse(resultType, true, out GoalType goal))
                {
                    return goal.IsTileKind();
                }
                return false;
            }).ToList();
            
            AppendTileKinds(tileKindResults, stringBuilder, reportConfig.EasyThreshold, stats);
            AppendTileKinds(tileKindResults, stringBuilder, reportConfig.MediumThreshold, stats);
            AppendTileKinds(tileKindResults, stringBuilder, reportConfig.HardThreshold, stats);
            
            var notGridBasedResults = stats.ResultTypes.Where(resultType =>
            {
                if (Enum.TryParse(resultType, true, out GoalType goal))
                {
                    return !goal.IsGridBased();
                }
                return false;
            }).ToList();
            
            var gridBasedResults = stats.ResultTypes.Where(resultType =>
            {
                if (Enum.TryParse(resultType, true, out GoalType goal))
                {
                    return goal.IsGridBased();
                }
                return false;
            }).ToList();

            AppendResultsReachedComment(notGridBasedResults, stringBuilder, stats, goalDict);
            AppendResultsReachedComment(gridBasedResults, stringBuilder, stats, goalDict);
            
            stringBuilder.Append($"{stats.CountAllEqualOrGreaterThenCeiling()}" +
                                 $"/" +
                                 $"{stats.MaxCount()} \t");
            
            stringBuilder.Append(stats.GetLossReasonString() + "\t");

            var remainingMoves = (float)(stats.GetResults("RemainingMoves")?.Average() ?? 0f);
            stringBuilder.Append(remainingMoves.ToString("F2") + "\t");
            var sbUsed = (float)(stats.GetResults("SuperBoostUsed")?.Average() ?? 0f);
            stringBuilder.Append(sbUsed.ToString("F2") + "\t");
            var lbCreated = (float)(stats.GetResults("LineBreaker")?.Average() ?? 0f);
            stringBuilder.Append(lbCreated.ToString("F2") + "\t");
            var bombCreated = (float)(stats.GetResults("Bomb")?.Average() ?? 0f);
            stringBuilder.Append(bombCreated.ToString("F2") + "\t");
            var boltCreated = (float)(stats.GetResults("Bolt")?.Average() ?? 0f);
            stringBuilder.Append(boltCreated.ToString("F2") + "\t");
            var propellerCreated = (float)(stats.GetResults("Propeller")?.Average() ?? 0f);
            stringBuilder.Append(propellerCreated.ToString("F2") + "\t");
            var reshuffles = (float)(stats.GetResults("Shuffles")?.Average() ?? 0f);
            stringBuilder.Append(reshuffles.ToString("F2") + "\t");
            var emptyMoves = (float)(stats.GetResults("EmptyMoves")?.Average() ?? 0f);
            stringBuilder.Append(emptyMoves.ToString("F2") + "\t");
            var minTurns = (float)(stats.GetResults("Turns")?.Min() ?? 0f);
            stringBuilder.Append(minTurns.ToString("F2") + "\t");
            var avgTurns = (float)(stats.GetResults("Turns")?.Average() ?? 0f);
            stringBuilder.Append(avgTurns.ToString("F2") + "\t");
            var medTurns = stats.GetThresholdForRatio("Turns", 0.5f);
            stringBuilder.Append(medTurns.ToString("F2") + "\t");
            return stringBuilder.ToString();
        }
        
        private static void AppendTileKinds(IList<string> tileKindResults, StringBuilder stringBuilder, float ratio, Match3Statistics stats)
        {
            var tileKindString = string.Empty;
            if (tileKindResults.Count > 0)
            {
                tileKindString += "{";
                foreach (var tileKindResult in tileKindResults)
                {
                    var count = stats.GetThresholdForRatio(tileKindResult, ratio).ToString();
                    tileKindString += $"\"{tileKindResult}\":{count}, ";
                }

                tileKindString = tileKindString.TrimEnd(' ').TrimEnd(',');
                tileKindString += "}";
            }

            stringBuilder.Append(tileKindString + "\t");

        }
        
        private static void AppendResultsReachedComment(IList<string> results, StringBuilder stringBuilder, Match3Statistics stats, IDictionary<string,int> goalDict)
        {
            var goalString = string.Empty;
            if (results.Count > 0)
            {
                foreach (var result in results)
                {
                    var reachCount = stats.CountEqualOrGreaterThenCeiling(result);
                    var count = stats.Count(result);
                    var goalCount = goalDict[result];
                    goalString += $"Config {result} goal equals {goalCount} and reached in {reachCount}/{count} times; ";
                }

                goalString = goalString.TrimEnd(' ').TrimEnd(';');
            }

            goalString += "\t";

            stringBuilder.Append(goalString);
        }
        
    }
}
#endif