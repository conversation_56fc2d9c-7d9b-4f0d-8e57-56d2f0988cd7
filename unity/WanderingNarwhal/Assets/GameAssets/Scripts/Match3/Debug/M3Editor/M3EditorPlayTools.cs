#if UNITY_EDITOR
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.Match3.Systems.GoalsService;
using BBB.UI.Level.Input;
using GameAssets.Scripts.Match3.Settings;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorPlayTools : M3EditorBase
    {
        private Dropdown _dropDownStatisticsType;

        public M3EditorPlayTools(M3Editor m3Editor) : base(m3Editor)
        {

        }

        public override void SetUpUi()
        {
            base.SetUpUi();

            var children = _m3Editor.GetComponentsInChildren<Button>(true);
            foreach (var child in children)
            {
                if (child.name == "AiMove")
                {
                    ObjectsEnableDuringPlay.Add(child.gameObject);
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(AiMove);
                }

                if (child.name == "Shuffle")
                {
                    ObjectsEnableDuringPlay.Add(child.gameObject);
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(Shuffle);
                }

                if (child.name == "LightningRain")
                {
                    ObjectsEnableDuringPlay.Add(child.gameObject);
                    child.onClick.RemoveAllListeners();
                    child.onClick.AddListener(LightningRain);
                }
            }

            var dropDownObject = GameObject.Find( "StatisticsTypeDropDown");
            if (dropDownObject != null)
            {
                _dropDownStatisticsType = dropDownObject.GetComponent<Dropdown>();
            }

            foreach(var go in ObjectsEnableDuringPlay)
                go.SetActive(false);
        }

        private void AiMove()
        {
            if (_m3Editor.GameController.IsInputLocked)
                return;

            var movePickLogic = PickLogic.Heuristic;
            if (_dropDownStatisticsType != null)
            {
                movePickLogic = (PickLogic)_dropDownStatisticsType.value;
            }

            var context = _m3Editor.Context;
            var movePicker = context.Resolve<HeuristicMovePicker>();
            var goalsSystem = context.Resolve<GoalsSystem>();
            var spawnSystem = context.Resolve<M3SpawnSystem>().Clone();
            var inputController = context.Resolve<IInputController>();
            var gameController = context.Resolve<GameController>();
            var tileResources = context.Resolve<TileResourceSelector>();
            var superBoostSystem = context.Resolve<SuperBoostSystem>();
            var level = GameController.Level;

            PossibleMove bestMove;

            switch (movePickLogic)
            {
                case PickLogic.Random:
                    var allPossibleMatches = SearchMatchesSystem.SearchForAllPossibleMoves(level.Grid);
                    bestMove = allPossibleMatches.GetRandomItem();
                    break;
                case PickLogic.Heuristic:
                    bestMove = movePicker.GetBestPossibleMove(false, level.Grid, level.Goals, goalsSystem);
                    break;
                case PickLogic.HeuristicMax:
                    bestMove = movePicker.GetBestPossibleMove(true, level.Grid, level.Goals, goalsSystem);
                    break;
                case PickLogic.Optimal:
                    bestMove = OptimalMovePicker.GetBestPossibleMove(level, level.Grid, goalsSystem, spawnSystem, gameController, tileResources);
                    break;
                case PickLogic.UsefulMovesWin:
                    bestMove = UsefulMovesPicker.GetBestPossibleMove(level, level.Grid, goalsSystem, spawnSystem, gameController, tileResources);
                    break;
                case PickLogic.UsefulMovesLose:
                    bestMove = UsefulMovesPicker.GetBestPossibleMove(level, level.Grid, goalsSystem, spawnSystem, gameController, tileResources);
                    break;
                default:
                    bestMove = movePicker.GetBestPossibleMove(false, level.Grid, level.Goals, goalsSystem);
                    break;
            }

            if (bestMove.Type == PossibleMoveType.DoubleTap)
            {
                inputController.AutoDoubleTap(bestMove.FirstCell.Coords);
            }
            else
            {
                inputController.AutoSwap(bestMove.FirstCell.Coords, bestMove.SecondCell.Coords);
            }

            if (superBoostSystem.AllowedToUse)
            {
                superBoostSystem.UseSuperBoost();
            }
        }

        private void LightningRain()
        {
            var playerInput = new PlayerInputItemRain(0.5f);
            var pss = _m3Editor.Context.Resolve<Match3SimulationPlayer>();
            pss.SimulateSync(GameController.OriginalGrid, GameController.Grid, GameController.Level, playerInput, GameController.RemainingMoves);
        }

        private void Shuffle()
        {
            var playerInput = new PlayerInputItemReshuffle();
            var simulationPlayer = _m3Editor.Context.Resolve<Match3SimulationPlayer>();
            simulationPlayer.SimulateSync(GameController.OriginalGrid, GameController.Grid, GameController.Level, playerInput, GameController.RemainingMoves);
        }
    }
}
#endif