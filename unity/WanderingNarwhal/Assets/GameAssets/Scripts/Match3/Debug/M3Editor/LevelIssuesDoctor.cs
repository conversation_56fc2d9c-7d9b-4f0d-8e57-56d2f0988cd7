using System;
using System.Collections.Generic;
using System.Linq;
using BBB.CellTypes;
using BBB.GameAssets.Scripts.Player;
using BBB.Match3.Renderer;
using BBB.Match3.Systems;
using BBB.Match3.Systems.CreateSimulationSystems;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.M3Editor
{
    public static class LevelIssuesDoctor
    {
        public static void CheckInvalidTilesStates(ILevel level)
        {
            foreach (var cell in level.Grid.Cells)
            {
                var tile = cell.Tile;
                if (tile == null) continue;

                tile.CheckInvalidStates(level, cell);
            }
        }
        
        public static void CheckForBackgroundInteraction(ILevel level)
        {
            foreach (var cell in level.Grid.Cells)
            {
                if (cell.IsAnyOf(CellState.BackDouble | CellState.BackOne | CellState.Petal))
                {
                    if (cell.HasTile() && cell.Tile.PreventBackgroundInteraction())
                    {
                        Debug.LogError($"{cell.Tile.Speciality} on grass in level '{level.Config.Uid}'");
                    }
                    
                    if (cell.PreventBackgroundInteraction())
                    {
                        Debug.LogError("Water or FlagEnd on grass in level " + level.LevelUid);
                    }
                }
            }
        }

        public static void CheckDespawnerColumnRules(ILevel level)
        {
            for (var x = 0; x < level.Grid.Width; x++)
            {
                var despawnerCount = 0;
                for (var y = 0; y < level.Grid.Height; y++)
                {
                    var cell = level.Grid.GetCell(new Coords(x, y));
                    if (cell != null && cell.IsAnyOf(CellState.Despawner))
                    {
                        despawnerCount++;

                        if (level.Grid.TryGetCell(new Coords(x, y - 1), out var cellBelow))
                        {
                            var hasIndestructibleState = cellBelow.PreventBackgroundInteraction();
                            var hasIndestructibleTile =
                                cellBelow.HasTile() && cellBelow.Tile.IsIndestructibleBlocker();

                            if (!hasIndestructibleState && !hasIndestructibleTile)
                            {
                                Debug.LogError($"Column {x} in level '{level.Config.Uid}' the cell below de-spawner is not indestructible blocker." +
                                               $"Consider adding/replace the tile below the de-spawner");
                            }
                        }
                    }
                }

                if (despawnerCount > 1)
                {
                    Debug.LogError($"Column {x} in level '{level.Config.Uid}' has more than one de-spawner.");
                }
            }
        }

        public static void CheckGoalsInLevel(ILevel level, LevelRemoteData remoteData, SpawnerSettings[] spawners)
        {
            var levelName = $"{level.LevelUid}.{level.Stage + 1}";
            var remoteGoalData = remoteData.GetGoalData();

            var goalsInRemoteData = new Dictionary<string, int>();
            var goalTypes = GoalTypeExtensions.TileKindTypes()
                .Concat(GoalTypeExtensions.GridBasedTypes());

            foreach (var goalType in goalTypes)
            {
                var count = level.Goals.GetGoalValue(goalType);
                
                var shouldAdd = count > 0 ||
                                (goalType == GoalType.Backgrounds && level.Goals.GetGoalValue(GoalType.Bush) > 0) ||
                                (goalType == GoalType.Petal && level.Goals.GetGoalValue(GoalType.FlowerPot) > 0);

                if (shouldAdd)
                {
                    if (!goalsInRemoteData.TryAdd(goalType.ToString(), count))
                    {
                        Debug.LogError(
                            $"Level '{levelName}' contains multiple goals of type {goalType}\n" +
                            string.Join(", ", goalsInRemoteData.Select(kvp => $"{kvp.Key} = {kvp.Value}")));
                    }
                }
            }

            if (goalsInRemoteData.Count > 4)
            {
                Debug.LogError(
                    $"Level '{levelName}' has {goalsInRemoteData.Count} goals, but only 4 are allowed.\n" +
                    string.Join(", ", goalsInRemoteData.Select(kvp => $"{kvp.Key} = {kvp.Value}")));
            }
            
            if (!level.Goals.AnyGoalsLeft())
            {
                Debug.LogError($"Level '{levelName}' does not have goals!");
            }

            var goalsTypes = Enum.GetNames(typeof(GoalType));
            foreach (var goalStr in goalsTypes)
            {
                var goal = (GoalType)Enum.Parse(typeof(GoalType), goalStr);
                if (goal.IsTileKind() || goal == GoalType.Score) continue;

                var goalValue = level.Goals.GetGoalValue(goal);
                var gridValue = level.Goals.DebugCalculateExpectedGoalTargetCountFromCurrentGrid(level.Grid, goal, goalValue);
                if (goalValue == gridValue)
                {
                    foreach (var remoteGoal in remoteGoalData.TileKindGoals)
                    {
                        if (level.Goals.DoesGoalExist(remoteGoal.Key))
                        {
                            var remoteValue = remoteGoal.Value;
                            var levelValue = level.Goals.GetGoalValue(remoteGoal.Key);
                            if (remoteValue > 0 && remoteValue != levelValue)
                            {
                                Debug.LogError(
                                    $"Level '{levelName}: '{remoteGoal.Key}' goal has remote config value {remoteValue}" +
                                    $"\nbut it is changed to {levelValue} during level preprocess.");
                            }
                        }
                        else
                        {
                            Debug.LogError(
                                $"Level '{levelName}: Remote data contains goal {remoteGoal.Key}," +
                                "but it was removed on level preprocess due to grid state" +
                                "\n(grid doesn't contain related tile and it will not spawn during gameplay).");
                        }
                    }
                }
                else
                {
                    var isSpawnedOnGrid = false;
                    if (goal.IsSpawnable())
                    {
                        var spawnersCache = level.Grid.Cells.Where(cell => cell.IsAnyOf(CellState.Spawner)).ToList();
                        foreach (var spawnerCell in spawnersCache)
                        {
                            var spawnerSetting = SpawnerSettings.FindSpawnerByUid(spawners, spawnerCell.SpawnerUid);
                            if (spawnerSetting == null)
                            {
                                Debug.LogError(
                                    $"M3: Found spawner on grid with non-existent spawner uid {spawnerCell.SpawnerUid}, max allowed uid = {spawners.Length}");
                                spawnerSetting = SpawnerSettings.FindSpawnerByUid(spawners, 0);
                            }

                            if (spawnerSetting == null) continue;
                            if (spawnerSetting.IsSpawningAnyTileRelatedToGoal(goal))
                            {
                                isSpawnedOnGrid = true;
                                break;
                            }
                        }
                    }

                    if (!isSpawnedOnGrid && goalValue > 0)
                    {
                        // SK: this check should not be applied for skunks (discussed 07-Sep-2022)
                        if (goal != GoalType.Skunk)
                        {
                            Debug.LogError(
                                $"Level '{levelName}': Number of '{goal}' goals is not equal to items on grid. LevelConfig: {goalValue}. Grid: {gridValue}.");
                        }
                    }
                }
            }
        }

        public static void CheckDropItemsHaveExit(ILevel level, string filePath)
        {
            foreach (var cell in level.Grid.Cells)
            {
                if (cell == null || !cell.HasTile() || cell.Tile.Speciality != TileSpeciality.DropItem)
                {
                    continue;
                }

                var stopped = false;
                var currentPos = cell.Coords;

                while (currentPos.Y >= 0)
                {
                    var currentCell = level.Grid.GetCell(currentPos);

                    // empty cell is passable
                    if (currentCell == null)
                    {
                        if (AreAllEmptyUnder(level.Grid, currentPos))
                        {
                            if(level.Grid.TryGetCell(currentPos - new Coords(1, 0), out var leftCell)
                               && !leftCell.IsAnyOf(CellState.NotAcceptingTiles) && !leftCell.HasMultiSizeCellReference())
                            {
                               currentPos -= new Coords(1,0);
                               continue;
                            }

                            if(level.Grid.TryGetCell(currentPos + new Coords(1, 0), out var rightCell)
                               && !rightCell.IsAnyOf(CellState.NotAcceptingTiles) && !rightCell.HasMultiSizeCellReference())
                            {
                                currentPos += new Coords(1,0);
                                continue;
                            }

                            break;
                        }

                        currentPos.Y -= 1;
                        continue;
                    }

                    // reach despawner?
                    if (currentCell.IsAnyOf(CellState.Despawner))
                    {
                        stopped = true;
                        break;
                    }

                    // bumped into the wall?
                    if (currentCell.HasAnyWall(CardinalDirections.S))
                    {
                        Debug.LogError(
                            $"Level: {level.LevelUid} -- {filePath}.\nDropItem from position {cell.Coords} cannot reach a despawner due to a Wall at {currentPos}!");
                        stopped = true;
                        break;
                    }

                    // bumped into non-destroyable hive?
                    if (currentCell.HasTile() && currentCell.Tile.Speciality == TileSpeciality.Hive)
                    {
                        Debug.LogError(
                            $"Level: {level.LevelUid}-- {filePath}.\nDropItem from position {cell.Coords} cannot reach a despawner due to a Hive at {currentPos}!");
                        stopped = true;
                        break;
                    }

                    // None of those, continue falling
                    currentPos.Y -= 1;
                }

                if (!stopped)
                {
                    currentPos.Y += 1;
                    Debug.LogError(
                        $"Level: {level.LevelUid} -- {filePath}.\nDropItem from position {cell.Coords} reached the end of its path at {currentPos} without any despawners!");
                }
            }
        }

        private static bool AreAllEmptyUnder(Grid grid, Coords pos)
        {
            bool allEmpty = true;
            while (pos.Y >= 0)
            {
                pos.Y -= 1;

                allEmpty &= !grid.Contains(pos);
            }

            return allEmpty;
        }

        public static void CheckTntTargets(ILevel level, SpawnerSettings[] globalSpawners)
        {
            // Collect all level spawners and Tnt cells
            var levelSpawners = new HashSet<SpawnerSettings>();
            var tntCells = new List<Cell>();
            foreach (var cell in level.Grid.Cells)
            {
                if (cell.IsAnyOf(CellState.Spawner))
                {
                    var spawnerSettings = SpawnerSettings.FindSpawnerByUid(globalSpawners, cell.SpawnerUid);
                    if (spawnerSettings != null)
                        levelSpawners.Add(spawnerSettings);
                }

                if (cell.IsAnyOf(CellState.Tnt))
                    tntCells.Add(cell);
            }

            foreach (var tntCell in tntCells)
            {
                int matchingTiles;
                var tntKind = tntCell.TntKind;

                if (!level.UsedKinds.Contains(tntKind))
                {
                    ErrorMessage(tntCell, tntKind);
                }

                if (tntCell.TntTarget == TntTargetType.Simple)
                {
                    var spawnKind = levelSpawners.Any(ss => ss.TilesSettings.Any(
                        ts => ts.Asset == TileAsset.Simple &&
                              (ts.Kind == tntKind || ts.Kind == TileKinds.Undefined)));

                    // If there is a spawner that can spawn target kind, we consider this case as valid
                    if (spawnKind) continue;

                    matchingTiles = level.Grid.Cells.Count(cell => cell.HasTile() && cell.Tile.Kind == tntKind);
                }
                else
                {
                    matchingTiles = level.Grid.Cells.Count(cell => cell.HasTile() &&
                       TntCellLayer.IsTileMatchTargetType(
                           tntCell.TntTarget, cell.TntKind, cell.Tile.Asset, cell.Tile.Kind));
                }

                if (matchingTiles >= tntCell.TntCount)
                    continue;
                
                ErrorMessage(tntCell, tntKind);
            }

            void ErrorMessage(Cell tntCell, TileKinds tntKind)
            {
                Debug.LogError($"Level {level.LevelUid}.{level.Stage + 1}:\n" +
                               $"There are not enough board tiles or matching spawners for TNT at {tntCell.Coords}({tntKind}).");
            }
        }

        public static void CheckForColoredTargets(ILevel level, SpawnerSettings[] globalSpawners)
        {
            // Collect all level spawners and Colored Tiles
            var levelSpawners = new HashSet<SpawnerSettings>();

            var dictionary = new Dictionary<Cell, Tuple<TileKinds[], int>>();

            foreach (var cell in level.Grid.Cells)
            {
                if (cell.IsAnyOf(CellState.Spawner))
                {
                    var spawnerSettings = SpawnerSettings.FindSpawnerByUid(globalSpawners, cell.SpawnerUid);
                    if (spawnerSettings != null)
                        levelSpawners.Add(spawnerSettings);
                }
                
                var mainCell = cell.GetMainCellReference(out _);
                if (!mainCell.HasTile()) continue;
                var tile = mainCell.Tile;

                switch (tile.Speciality)
                {
                    case TileSpeciality.Soda:
                        var currentCount = tile.GetParam(TileParamEnum.SodaBottlesCount);
                        var currentState = tile.GetParam(TileParamEnum.SodaColors);
                        var currentColors = new TileKinds[currentCount];
                        SodaTileLayer.ExtractTileKindsFromInt(currentState, currentColors, currentCount);
                        dictionary.Add(cell, new Tuple<TileKinds[], int>(currentColors, currentCount));
                        break;

                    case TileSpeciality.DynamiteBox:
                        currentCount = tile.GetParam(TileParamEnum.DynamiteSticksCount);
                        currentState = tile.GetParam(TileParamEnum.DynamiteBoxColors);
                        currentColors = new TileKinds[currentCount];
                        DynamiteBoxTileLayer.ExtractTileKindsFromInt(currentState, currentColors, currentCount);
                        dictionary.Add(cell, new Tuple<TileKinds[], int>(currentColors, currentCount));
                        break;

                    case TileSpeciality.Squid:
                        var sizeX = tile.GetParam(TileParamEnum.SizeX);
                        var sizeY = tile.GetParam(TileParamEnum.SizeY);
                        currentCount = tile.GetParam(TileParamEnum.SquidsCount);
                        currentState = tile.GetParam(TileParamEnum.SquidsState);
                        currentColors = new TileKinds[Mathf.Min(sizeX * sizeY, currentCount)];
                        SquidTileLayer.ExtractTileKindsFromInt(currentState, currentColors, currentCount);
                        dictionary.Add(cell, new Tuple<TileKinds[], int>(currentColors, currentCount));
                        break;
                    
                    case TileSpeciality.TukTuk:
                        currentCount = tile.GetParam(TileParamEnum.TukTukCount);
                        var currentColor = tile.GetParam(TileParamEnum.TukTukColor);
                        dictionary.Add(cell, new Tuple<TileKinds[], int>(new []{(TileKinds)currentColor}, currentCount));
                        break;
                    
                    case TileSpeciality.ColorCrate:
                        dictionary.Add(cell, new Tuple<TileKinds[], int>(new []{cell.Tile.Kind}, 1));
                        break;
                }
            }
            
            foreach (var (key, (tileKinds, count)) in dictionary)
            {
                foreach (var tileKind in tileKinds)
                {
                    if (!level.UsedKinds.Contains(tileKind))
                    {
                        ErrorMessage(key, tileKind);
                    }
                    
                    var spawnKind = levelSpawners.Any(ss => ss.TilesSettings.Any(
                        ts => ts.Asset == TileAsset.Simple &&
                              (ts.Kind == tileKind || ts.Kind == TileKinds.Undefined)));
                    
                    // If there is a spawner that can spawn target kind, we consider this case as valid
                    if (spawnKind)
                        continue;

                    var matchingTiles = level.Grid.Cells.Count(cell => cell.HasTile() && cell.Tile.Kind == tileKind);

                    if (matchingTiles >= count)
                        continue;
                    
                    ErrorMessage(key, tileKind);
                }
            }
            
            void ErrorMessage(Cell cell, TileKinds kinds)
            {
                Debug.LogError($"Level {level.LevelUid}.{level.Stage + 1}:\n" +
                               $"There are not enough board tiles or matching spawners for {cell.Tile.Speciality} at {cell.Coords}({kinds}).");
            }
        }
    }
}
