#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using System.Linq;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.M3Editor
{
    public class M3EditorConfigsStickerSkin : M3EditorConfigsItemBase
    {
        private readonly Dictionary<StickerSkin, Toggle> _stickerSkinToggles;

        public M3EditorConfigsStickerSkin(M3Editor m3Editor, M3EditorConfigs m3EditorConfigs) : base(m3Editor, m3EditorConfigs)
        {
            _stickerSkinToggles = new Dictionary<StickerSkin, Toggle>();
        }

        public override void SetUpUi()
        {
            foreach (var tf in M3Editor.GetComponentsInChildren<Transform>(true))
            {
                if (tf.name == "StickerSkinGroup")
                {
                    for (int i = 0; i < tf.childCount; i++)
                    {
                        var child = tf.GetChild(i);
                        var toggle = child.GetComponent<Toggle>();
                        toggle.onValueChanged.RemoveAllListeners();
                        toggle.onValueChanged.AddListener(OnToggleValueChanged);
                        StickerSkin skin;
                        if (!Enum.TryParse(toggle.name, out skin))
                        {
                            Debug.LogError($"Can't parse name {toggle.name}", toggle);
                            skin = i == 0 ? StickerSkin.Crate : StickerSkin.Slate;
                        }

                        _stickerSkinToggles[skin] = toggle;
                    }
                }
            }
        }

        private void OnToggleValueChanged(bool value)
        {
            if (!value)
                return;

            var firstToggleOn = _stickerSkinToggles.Values.FirstOrDefault(t => t.isOn);

            if (firstToggleOn != null)
            {
                var name = firstToggleOn.name;
                M3Editor.GameController.Level.StickerSkin = name.TryParseToEnum<StickerSkin>();
            }
            else
                Debug.LogError("No sticker skin toggle on is found");

            RefreshToggleColors();
            M3EditorConfigs.ApplyConfigs();
        }

        private void RefreshToggleColors()
        {
            foreach (var toggleKvp in _stickerSkinToggles)
            {
                var toggle = toggleKvp.Value;
                var cb = toggle.colors;
                bool isOn = toggle.isOn;
                cb.normalColor = isOn ? Color.black : Color.white;
                cb.highlightedColor = isOn ? Color.black : Color.white;
                toggle.colors = cb;
            }
        }

        public override void LoadConfigsFrom(ILevel level)
        {
            foreach (var kvp in _stickerSkinToggles)
            {
                var toggle = kvp.Value;
                toggle.isOn = level.StickerSkin == kvp.Key;
            }

            RefreshToggleColors();
        }
    }
}
#endif