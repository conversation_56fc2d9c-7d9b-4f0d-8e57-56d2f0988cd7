using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace BBB.UI
{
    public class AutoBoosterItems : BbbMonoBehaviour
    {
        public float Value;
        public Button ActionButton;
        [SerializeField] private TextMeshProUGUI _displayLabel;
        [SerializeField] private TMP_InputField _inputField;
        [SerializeField] private TextMeshProUGUI _buttonLabel;

        private void Start()
        {
            _inputField.onValueChanged.RemoveAllListeners();
            _inputField.onValueChanged.AddListener(OnValueChanged);
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            _inputField.onValueChanged.RemoveAllListeners();
        }

        public void ResetInputField()
        {
            _inputField.text = "";
        }
        
        public void Setup(string boosterUid, string buttonLabel)
        {
            _displayLabel.text = boosterUid;
            _buttonLabel.text = buttonLabel;
            gameObject.name = boosterUid;
            ResetInputField();
        }
        
        private void OnValueChanged(string arg0)
        {
            if (float.TryParse(arg0, out var value))
            {
                Value = value;
            }
        }
    }
}