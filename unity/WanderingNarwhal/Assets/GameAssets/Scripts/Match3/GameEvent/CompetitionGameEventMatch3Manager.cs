using System;
using BBB.DI;

namespace BBB
{
    public class CompetitionGameEventMatch3Manager : IGameEventMatch3Manager
    {
        public event Action OnSetupEvent;

        private IGameEventManager _gameEventManager;
        private bool _canLevelsBeGathered;
        private int _scoreMult = 1;
        private bool _isStartedLevelPlay;
        private string _activeGameEventUid;

        private int _collectedScores;

        public int SortOrder => 0;
        public bool IsNormalTileReskinGameEventType => NormalTileSelectedForReskinOnThisLevel != TileKinds.None;

        public TileKinds NormalTileSelectedForReskinOnThisLevel { get; set; }

        public GameEventBase ActiveGameEvent =>
            //don't keep the reference to the game event because it may be changed on the fly from the GameEventManager
            _gameEventManager.FindGameEventByUid(_activeGameEventUid);

        public string ActiveGameEventUid => _activeGameEventUid ?? string.Empty;

        public int LastCollectedScores { get; private set; }

        public int CollectedScores => _collectedScores * _scoreMult;

        public int ScoresThatWouldBeLost
        {
            get
            {
                if (ActiveGameEvent is not CompetitionGameEvent activeGameEvent)
                    return 0;
                
                if (!activeGameEvent.IntroductionAlreadyShown)
                    return _collectedScores * _scoreMult;

                return _collectedScores * _scoreMult + activeGameEvent.ScoreThatWouldBeLost;
            }
        }

        public bool WillCompetitionProgressBeLost
        {
            get
            {
                if (ActiveGameEvent is not CompetitionGameEvent activeGameEvent)
                    return false;

                return activeGameEvent.IntroductionAlreadyShown && activeGameEvent.ScoreThatWouldBeLost > 0;
            }
        }
        public virtual bool IsAlternateMapScreen => false;
        
        public virtual void Init(IContext context)
        {
            _gameEventManager = context.Resolve<IGameEventManager>();
            SetActiveGameEvent();
        }

        protected virtual void SetActiveGameEvent()
        {
            var gameplayType = GetGameplayType();
            var activeGameEvent = _gameEventManager
                .GetHighestPriorityEvent(ev => ev.Status == GameEventStatus.Active && ev.GameplayType == gameplayType);

            if (activeGameEvent != null)
            {
                if (activeGameEvent is CompetitionGameEvent @event)
                {
                    _activeGameEventUid = @event.Uid;
                }
                else
                {
                    throw new InvalidCastException($"Can not use {activeGameEvent.GetType()} in CompetitionGameEvent");
                }
            }
        }

        public void AddGameEventScore(int score)
        {
            if (ActiveGameEvent != null)
            {
                _collectedScores += score;
            }
        }

        public virtual GameEventGameplayType GetGameplayType()
        {
            return GameEventGameplayType.Competition;
        }

        public void Setup(ILevel level)
        {
            _isStartedLevelPlay = false;
            _canLevelsBeGathered = _gameEventManager.CanLabelsBeGatheredAtLevel(level.Config.Uid);

            OnSetupEvent?.Invoke();

            if (!_canLevelsBeGathered)
            {
                _activeGameEventUid = null;
            }
            else if (_activeGameEventUid.IsNullOrEmpty())
            {
                SetActiveGameEvent();
            }

            _scoreMult = ActiveGameEvent is { Status: GameEventStatus.Active } && ActiveGameEvent.IsMultiplierScoreStreakActive() ?  ActiveGameEvent.GetScoreMultiplier(true) : 1;
        }

        public void SubmitScore(int score, bool append = false)
        {
            LastCollectedScores = append ? LastCollectedScores + score : score;
            var activeGameEvent = ActiveGameEvent as CompetitionGameEvent;
            activeGameEvent?.AddScore(score, append);
            if (activeGameEvent != null && activeGameEvent.ShouldAutoSubmit()
                                        && score > 0)
            {
                activeGameEvent.EventLeaderboard.SubmitCurrentScore(null);
            }
        }

        public virtual void FinalizeScore() { }

        public virtual void ProcessOnLevelWin()
        {
            var activeGameEvent = ActiveGameEvent as CompetitionGameEvent;
            activeGameEvent?.RestorePenalizablePart();
            if (_isStartedLevelPlay)
                activeGameEvent?.DecrementFailedAttempt();

            SubmitScore(_collectedScores * _scoreMult);

            Clear();
        }

        public virtual void ProcessOnLevelLose()
        {
            var activeGameEvent = ActiveGameEvent as CompetitionGameEvent;
            activeGameEvent?.RestorePenalizablePart();
            var scoreWasReset = activeGameEvent?.TryResetScore() ?? false;
            if (activeGameEvent != null && activeGameEvent.ShouldAutoSubmit() && scoreWasReset)
            {
                activeGameEvent.EventLeaderboard.SubmitCurrentScore(null);
            }

            Clear();
        }

        public void ProcessOnExit()
        {
            var activeGameEvent = ActiveGameEvent as CompetitionGameEvent;
            activeGameEvent?.RestorePenalizablePart();
            activeGameEvent?.TryResetScore();
            Clear();
        }

        public void ProcessOnShuffleFailed()
        {
            var activeGameEvent = ActiveGameEvent as CompetitionGameEvent;
            activeGameEvent?.RestorePenalizablePart();
            activeGameEvent?.DecrementFailedAttempt(isShuffleFailed: true);
            Clear();
        }

        public virtual bool MakeScoresPenalizable()
        {
            var activeGameEvent = ActiveGameEvent as CompetitionGameEvent;
            activeGameEvent?.CachePenalizablePart();
            return activeGameEvent != null;
        }

        public void ProcessOnFirstMove()
        {
            _isStartedLevelPlay = true;
            ActiveGameEvent?.IncrementFailedAttempt();
        }

        private void Clear()
        {
            _collectedScores = 0;
            _scoreMult = 1;
        }

        public virtual void HandleDebugWin()
        {
            if (ActiveGameEvent != null)
                _collectedScores += 10;
        }
    }
}
