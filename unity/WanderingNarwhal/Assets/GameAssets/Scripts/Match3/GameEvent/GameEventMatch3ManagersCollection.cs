using System;
using System.Collections;
using System.Collections.Generic;
using BBB.DI;

namespace BBB
{
    public sealed class GameEventMatch3ManagersCollection : IEnumerable<IGameEventMatch3Manager>, IContextInitializable
    {
        private readonly List<IGameEventMatch3Manager> _managersList = new List<IGameEventMatch3Manager>();

        public void InitializeByContext(IContext context)
        {
            foreach (var manager in _managersList)
            {
                manager.Init(context);
            }
        }
        
        public void AddManager(IGameEventMatch3Manager manager)
        {
            _managersList.Add(manager);
            SortManagers();
        }

        private void SortManagers()
        {
            _managersList.Sort((a,b) => a.SortOrder.CompareTo(b.SortOrder));
        }

        public void ForEveryManager(Action<IGameEventMatch3Manager> action)
        {
            foreach (var manager in _managersList)
            {
                action?.Invoke(manager);
            }
        }

        public bool IsAnyManager(Predicate<IGameEventMatch3Manager> managerPredicate)
        {
            foreach (var manager in _managersList)
            {
                if (managerPredicate(manager))
                {
                    return true;
                }
            }

            return false;
        }
        
        public IGameEventMatch3Manager GetFirstManager(Predicate<IGameEventMatch3Manager> managerPredicate)
        {
            foreach (var manager in _managersList)
            {
                if (managerPredicate(manager))
                {
                    return manager;
                }
            }

            return null;
        }

        public string GetFirstActiveGameEventUid()
        {
            foreach (var manager in _managersList)
            {
                var activeGameEventUid = manager.ActiveGameEventUid;
                return activeGameEventUid;
            }

            return string.Empty;
        }

        public GameEventBase GetFirstActiveGameEvent(Predicate<IGameEventMatch3Manager> managerPredicate)
        {
            foreach (var manager in _managersList)
            {
                if (managerPredicate(manager))
                {
                    return manager.ActiveGameEvent;
                }
            }

            return null;
        }

        public IEnumerator<IGameEventMatch3Manager> GetEnumerator()
        {
            return _managersList.GetEnumerator();
        }

        IEnumerator IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }
    }
}