using System.Collections.Generic;
using BBB.DI;
using BBB.TeamEvents;
using Beebopbee.Core.Extensions;
using Cysharp.Threading.Tasks;

namespace BBB
{
    public class TeamCoopEventMatch3Manager : ITeamCoopEventMatch3Manager, IContextInitializable
    {
        private ITeamEventManager _teamEventManager;
        private readonly List<TeamEvent> _activeEvents = new ();
        private bool _winRegistered;
        private bool _loseRegistered;
        private string _levelUid;

        public void InitializeByContext(IContext context)
        {
            _teamEventManager = context.Resolve<ITeamEventManager>();
        }

        public void Setup(ILevel level)
        {
            _levelUid = level.LevelUid;
            UpdateActiveGameEvents();
            _winRegistered = false;
            _loseRegistered = false;
        }

        private void UpdateActiveGameEvents()
        {
            _activeEvents.Clear();
            foreach (var ev in _teamEventManager.Events)
            {
                if (ev.Joined)
                {
                    _activeEvents.Add(ev);
                }
            }
        }

        public void HandleDebugWin()
        {
            
        }

        public void ProcessOnLevelWin()
        {
            HandleWin();
        }

        private void HandleWin()
        {
            if (_winRegistered)
            {
                return;
            }
            
            foreach (var ev in _activeEvents)
            {
                var success = ev.AddScore(_levelUid);
                if(success)
                    ev.SubmitScore(null).Forget();
            }
            
            _winRegistered = true;
        }

        public void ProcessOnExit(bool isStartedLevelPlay)
        {
            ProcessFailure(isStartedLevelPlay);
        }

        public void ProcessOnLevelLose()
        {
            ProcessFailure(true);
        }
        
        private void ProcessFailure(bool isStartedLevelPlay)
        {
            if (_loseRegistered)
            {
                return;
            }
            
            //might use this later if any loss related behaviour will be required

            _loseRegistered = true;
        }

        public int EventScoreThatWillBeLost()
        {
            if (_levelUid.IsNullOrEmpty()) return -1;
            
            foreach (var ev in _activeEvents)
            {
                return ev.GetScoreValue(_levelUid);
            }

            return -1;
        }
    }
}