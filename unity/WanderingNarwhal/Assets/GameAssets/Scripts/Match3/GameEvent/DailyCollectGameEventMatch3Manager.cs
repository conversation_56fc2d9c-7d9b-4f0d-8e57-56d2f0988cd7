using System;
using BBB.Core;
using BBB.DI;
using GameAssets.Scripts.Match3.Settings;
using FBConfig;
using UnityEngine;

namespace BBB
{

    public class DailyCollectGameEventMatch3Manager : IGameEventMatch3Manager
    {
        private IConfig _config;
        private IGameEventManager _gameEventManager;
        private bool _canLevelsBeGathered;
        private int _scoreMult = 1;
        private bool _isStartedLevelPlay;

        private CollectionGameEvent _activeGameEvent;

        private int _initialLabelsCount;
        private int _labelsLeftToSpawnCount;
        private int _collectedTiles;
        private int _lastWonTileCount;
        private TilesResources _tilesResources;
        public int SortOrder => -100;
        public int LastCollectedScores => _lastWonTileCount;

        public int CollectedScores => _collectedTiles * _scoreMult;

        public int ScoresThatWouldBeLost => _collectedTiles * _scoreMult;

        public bool WillCompetitionProgressBeLost => false;
        public bool IsAlternateMapScreen => false;
        public bool IsPossibleToCollectGameEventScores => _canLevelsBeGathered;

        public bool IsNormalTileReskinGameEventType => false;

        public MilestoneTarget MilestoneTarget { get; internal set; }

        public TileKinds NormalTileSelectedForReskinOnThisLevel { get; set; }

        public GameEventBase ActiveGameEvent => _activeGameEvent;

        public string ActiveGameEventUid => _activeGameEvent == null ? "" : _activeGameEvent.Uid;

        public GameEventGameplayType GetGameplayType()
        {
            return GameEventGameplayType.Collection;
        }

        public event Action OnSetupEvent;

        public void Init(IContext context)
        {
            _gameEventManager = context.Resolve<IGameEventManager>();
            _tilesResources = context.Resolve<TilesResources>();
            SetActiveGameEvent();
            _config = context.Resolve<IConfig>();
        }

        private void SetActiveGameEvent()
        {
            var gameplayType = GetGameplayType();
            var activeGameEvent = _gameEventManager
                .GetHighestPriorityEvent(ev => ev.Status == GameEventStatus.Active && ev.GameplayType == gameplayType);

            if (activeGameEvent != null)
            {
                if (activeGameEvent is CollectionGameEvent @event)
                {
                    _activeGameEvent = @event;
                }
                else
                {
                    throw new InvalidCastException($"Can not use {activeGameEvent.GetType()} in CollectionGameEvent");
                }
            }
        }

        public void Setup(ILevel level)
        {
            _canLevelsBeGathered = false;
            _isStartedLevelPlay = false;
            if (_activeGameEvent != null)
            {
                var milestoneTarget = _activeGameEvent.GetCurrentMilestoneTarget();
                var colorTileIsGoal = milestoneTarget.IsGoalColorTile();
                if (colorTileIsGoal)
                {
                    _canLevelsBeGathered = _gameEventManager.CanLabelsBeGatheredAtLevel(level.Config.Uid)
                                           && level.DoesUseTileKind(milestoneTarget.ToTileKind());
                }
                else
                {
                    _canLevelsBeGathered = _gameEventManager.CanLabelsBeGatheredAtLevel(level.Config.Uid);
                }
            }

            OnSetupEvent?.Invoke();

            var metaConfig = _config.TryGetDefaultFromDictionary<GameEventMetaConfig>();

            if (metaConfig.BaseSpawnChance <= 0f)
            {
                BDebug.LogError(LogCat.Match3, $"Base spawn chance is {metaConfig.BaseSpawnChance}");
                return;
            }

            if (!_canLevelsBeGathered)
            {
                _activeGameEvent = null;
                return;
            }

            if (_activeGameEvent == null)
            {
                SetActiveGameEvent();
            }
            
            _scoreMult = _activeGameEvent is { Status: GameEventStatus.Active } && _activeGameEvent.IsMultiplierScoreStreakActive() ? _activeGameEvent.GetScoreMultiplier(true) : 1;

            var gameEventMatch3LabelCounters = _gameEventManager.GetMatch3DataFor(level.Config.Uid);

            if (gameEventMatch3LabelCounters != null)
            {
                foreach (var (eventUid, count) in gameEventMatch3LabelCounters)
                    if (eventUid == _activeGameEvent.Uid)
                    {
                        _labelsLeftToSpawnCount = count;
                        _initialLabelsCount = _labelsLeftToSpawnCount;
                        break;
                    }
            }
            else
            {
                _labelsLeftToSpawnCount = 0;
            }

            MilestoneTarget = _activeGameEvent?.GetCurrentMilestoneTarget() ?? default;
            _tilesResources.AddMilestoneTargetToReskinMap(ActiveGameEventUid, MilestoneTarget);
        }

        public void AddGameEventScore(int score)
        {
            if (_activeGameEvent != null)
            {
                _collectedTiles += score;
            }
        }

        public void SubmitScore(int score, bool append = false)
        {
            _gameEventManager.IncrementScores(_activeGameEvent.Uid, score);
            _lastWonTileCount = append ? _lastWonTileCount + score : score;
        }

        public void FinalizeScore() { }

        public void ProcessOnLevelWin()
        {
            if (_activeGameEvent != null)
            {
                SubmitScore(_collectedTiles * _scoreMult);
                if (_isStartedLevelPlay)
                    _activeGameEvent.DecrementFailedAttempt();
            }
            Clear();
        }

        public void ProcessOnLevelLose()
        {
            Clear();
        }

        public void ProcessOnExit()
        {
            Clear();
        }

        public void ProcessOnShuffleFailed()
        {
            _activeGameEvent?.DecrementFailedAttempt(isShuffleFailed: true);
            Clear();
        }

        public bool MakeScoresPenalizable()
        {
            return false;
        }

        public void ProcessOnFirstMove()
        {
            _isStartedLevelPlay = true;
            _activeGameEvent?.IncrementFailedAttempt();
        }

        private void Clear()
        {
            _initialLabelsCount = 0;
            _labelsLeftToSpawnCount = 0;
            _collectedTiles = 0;
            _scoreMult = 1;
        }

        public void HandleDebugWin()
        {
#if UNITY_EDITOR
            if (Input.GetKey(KeyCode.X))
            {
                _initialLabelsCount = 5000;
            }
#endif
            _collectedTiles = _initialLabelsCount;
        }
    }
}
