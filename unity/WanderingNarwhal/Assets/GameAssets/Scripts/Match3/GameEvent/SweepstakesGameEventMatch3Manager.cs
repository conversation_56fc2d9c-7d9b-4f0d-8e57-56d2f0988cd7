using System;
using BBB.Core.Analytics;
using BBB.Core.Wallet;
using BBB.DI;
using UnityEngine;

namespace BBB
{
    public class SweepstakesGameEventMatch3Manager : IGameEventMatch3Manager
    {
        private IGameEventManager _gameEventManager;
        private IPlayerManager _playerManager;
        private int _scoreMult = 1;
        private bool _isStartedLevelPlay;
        private int _scoresToCollect;
        
        private SweepstakesGameEvent _activeGameEvent;

        public int SortOrder => -100;
        public int LastCollectedScores => _scoresToCollect;
        public int CollectedScores => _scoresToCollect * _scoreMult;
        public int ScoresThatWouldBeLost => _scoresToCollect * _scoreMult;
        public bool WillCompetitionProgressBeLost => false;
        public bool IsAlternateMapScreen => false;
        public bool IsNormalTileReskinGameEventType => false;
        
        public TileKinds NormalTileSelectedForReskinOnThisLevel { get; set; }
        public GameEventBase ActiveGameEvent => _activeGameEvent;
        public string ActiveGameEventUid => _activeGameEvent == null ? string.Empty : _activeGameEvent.Uid;

        public virtual GameEventGameplayType GetGameplayType()
        {
            return GameEventGameplayType.Sweepstakes;
        }

        public event Action OnSetupEvent;

        public void Init(IContext context)
        {
            _gameEventManager = context.Resolve<IGameEventManager>();
            _playerManager = context.Resolve<IPlayerManager>();
            SetActiveGameEvent();
        }

        private void SetActiveGameEvent()
        {
            var gameplayType = GetGameplayType();
            var activeGameEvent = _gameEventManager
                .GetHighestPriorityEvent(ev => ev.Status == GameEventStatus.Active && ev.GameplayType == gameplayType);

            if (activeGameEvent != null)
            {
                if (activeGameEvent is SweepstakesGameEvent @event)
                {
                    _activeGameEvent = @event;
                }
                else
                {
                    throw new InvalidCastException($"Can not use {activeGameEvent.GetType()} in SweepstakesGameEvent");
                }
            }
        }

        public virtual void Setup(ILevel level)
        {
            _scoresToCollect = _gameEventManager.SweepStakesGameEventConfig.TicketAmounts(level.Config.Difficulty);
            _isStartedLevelPlay = false;

            OnSetupEvent?.Invoke();

            if (_activeGameEvent == null)
            {
                SetActiveGameEvent();
            }

            _scoreMult =
                _activeGameEvent is { Status: GameEventStatus.Active } &&
                _activeGameEvent.IsMultiplierScoreStreakActive()
                    ? _activeGameEvent.GetScoreMultiplier(true)
                    : 1;
        }

        public void AddGameEventScore(int score)
        {
        }

        public void SubmitScore(int score, bool append = false)
        {
            Analytics.LogEvent(new CurrencyFlowEvent(EventNames.Level,_playerManager.CurrentLevel.LevelUid, _playerManager.CurrentLevel.GetDifficultyLocalizationID().ToLower(), nameof(TransactionType.Earn), CustomEventParameters.SweepstakesEventTypeScore, 1));
            _gameEventManager.IncrementScores(_activeGameEvent.Uid, score);
        }

        public void FinalizeScore() { }

        public void ProcessOnLevelWin()
        {
            if (_activeGameEvent != null)
            {
                SubmitScore(_scoresToCollect * _scoreMult);
                if (_isStartedLevelPlay)
                    _activeGameEvent.DecrementFailedAttempt();
            }
            Clear();
        }

        public void ProcessOnLevelLose()
        {
            Clear();
        }

        public void ProcessOnExit()
        {
            Clear();
        }

        public void ProcessOnShuffleFailed()
        {
            _activeGameEvent?.DecrementFailedAttempt(isShuffleFailed: true);
            Clear();
        }

        public bool MakeScoresPenalizable()
        {
            return false;
        }

        public void ProcessOnFirstMove()
        {
            _isStartedLevelPlay = true;
            _activeGameEvent?.IncrementFailedAttempt();
        }

        private void Clear()
        {
            _scoresToCollect = 0;
            _scoreMult = 1;
            _isStartedLevelPlay = false;
        }

        public void HandleDebugWin()
        {
        }
    }
}