using BBB.EndGameEvents;

namespace BBB
{
    public class SideMapEventMatch3Manager : CompetitionGameEventMatch3Manager
    {
        public override bool IsAlternateMapScreen => true;

        public override GameEventGameplayType GetGameplayType()
        {
            return GameEventGameplayType.SideMap;
        }

        public override void ProcessOnLevelWin()
        {
            base.ProcessOnLevelWin();
            if (ActiveGameEvent is SideMapGameEvent sideMapGameEvent)
                sideMapGameEvent.CompleteRound();
        }
    }
}