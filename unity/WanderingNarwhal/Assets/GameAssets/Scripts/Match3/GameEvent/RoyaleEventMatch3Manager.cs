using System.Collections.Generic;
using BBB.DI;
using BBB.RaceEvents;

namespace BBB
{
    public class RoyaleEventMatch3Manager : IRoyaleEventMatch3Manager, IContextInitializable
    {
        private IRoyaleEventManager _royaleEventManager;
        private readonly List<RoyaleEvent> _activeEvents = new();

        //these flags are required to make sure the win and lose methods will be only called once
        private bool _winRegistered = false;
        private bool _loseRegistered = false;

        public bool ShouldActivateBackground
        {
            get
            {
                foreach (var ev in _royaleEventManager.GetAllEvents())
                {
                    if (ev.Joined)
                    {
                        return true;
                    }
                }

                return false;
            }
        }

        public void InitializeByContext(IContext context)
        {
            _royaleEventManager = context.Resolve<IRoyaleEventManager>();
        }

        public void Setup(ILevel level)
        {
            UpdateActiveGameEvents();
            _winRegistered = false;
            _loseRegistered = false;
        }

        public void HandleDebugWin()
        {
        }

        public int EventScoreThatWillBeLost()
        {
            foreach (var royaleEvent in _activeEvents)
            {
                if (royaleEvent.Joined)
                {
                    return royaleEvent.CurrentStep;
                }
            }

            return -1;
        }

        public void ProcessOnLevelWin()
        {
            HandleWin();
        }

        public void ProcessOnExit(bool isStartedLevelPlay)
        {
            ProcessFailure(isStartedLevelPlay);
        }

        public void ProcessOnLevelLose()
        {
            ProcessFailure(true);
        }

        private void UpdateActiveGameEvents()
        {
            _activeEvents.Clear();
            foreach (var ev in _royaleEventManager.GetAllEvents())
            {
                if (ev.Joined)
                {
                    _activeEvents.Add(ev);
                }
            }
        }

        public bool MakeEventsPenalizable()
        {
            var anyMade = false;
            foreach (var royaleEvent in _activeEvents)
            {
                if (!royaleEvent.IsLaunched())
                    continue;

                if (royaleEvent.CacheToPenalizableData())
                {
                    anyMade = true;
                }
            }

            if (anyMade)
            {
                _royaleEventManager.SuppressStatePenalty(true);
            }

            return anyMade;
        }

        public void RestorePenalizableScore()
        {
            _royaleEventManager.SuppressStatePenalty(false);
            foreach (var royaleEvent in _activeEvents)
            {
                if (royaleEvent.IsLaunched() && royaleEvent.Joined)
                {
                    royaleEvent.TryRestorePenalizableScore();
                }
            }
        }

        private void HandleWin()
        {
            if (_winRegistered)
                return;

            foreach (var royaleEvent in _activeEvents)
            {
                if (royaleEvent.IsLaunched() && royaleEvent.Joined)
                {
                    royaleEvent.WinStep();
                }
            }

            _winRegistered = true;
        }

        private void ProcessFailure(bool isStartedLevelPlay)
        {
            if (_loseRegistered)
                return;

            foreach (var royaleEvent in _activeEvents)
            {
                if (royaleEvent.IsLaunched())
                {
                    if (isStartedLevelPlay)
                    {
                        royaleEvent.ResetStep();
                    }
                }
            }

            _loseRegistered = true;
        }
    }
}