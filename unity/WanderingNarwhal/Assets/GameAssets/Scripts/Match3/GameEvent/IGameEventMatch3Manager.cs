using System;
using BBB.DI;
using BBB.EndGameEvents;
using BBB.RaceEvents;
using BBB.TeamEvents;

namespace BBB
{
    public static class GameEventMatch3ManagerFactory
    {
        public static GameEventMatch3ManagersCollection CreateManagersCollection(IGameEventManager gameEventManager, ScreenType screenType)
        {
            var result = new GameEventMatch3ManagersCollection();

            foreach (var gameEvent in gameEventManager.GetEvents(ev => ev.Status == GameEventStatus.Active && ev.CanShowInScreen(screenType)))
            {
                var gameEventMatch3Manager = CreateManagerBasedOnEvent(gameEvent);
                result.AddManager(gameEventMatch3Manager);
            }

            return result;
        }
        
        public static IGameEventMatch3Manager CreateHighestPriorityManager(IGameEventManager gameEventManager, ScreenType screenType)
        {
            var gameEvent = gameEventManager.GetHighestPriorityEvent(ev =>
                ev.Status == GameEventStatus.Active && ev.CanShowInScreen(screenType));

            var manager = CreateManagerBasedOnEvent(gameEvent);

            return manager;
        }

        private static IGameEventMatch3Manager CreateManagerBasedOnEvent(GameEventBase gameEvent)
        {
            switch (gameEvent)
            {
                case SweepstakesGameEvent _:
                    return new SweepstakesGameEventMatch3Manager();
                case CollectionGameEvent _:
                    return new DailyCollectGameEventMatch3Manager();
                case EndOfContentGameEvent _:
                    return new EndOfContentEventMatch3Manager();
                case SideMapGameEvent _:
                    return new SideMapEventMatch3Manager();
                default:
                    return new CompetitionGameEventMatch3Manager();
            }
        }

        public static IRaceEventMatch3Manager CreateRaceEventManager(IRaceEventManager raceEventManager)
        {
            return new SimpleRaceEventMatch3Manager();
        }

        public static IRoyaleEventMatch3Manager CreateRoyaleEventManager(IRoyaleEventManager royaleEventManager)
        {
            return new RoyaleEventMatch3Manager();
        }

        public static ITeamCoopEventMatch3Manager CreateTeamCoopEventMatch3Manager(ITeamEventManager teamEventManager)
        {
            return new TeamCoopEventMatch3Manager();
        }
    }

    public interface IGameEventMatch3Manager
    {
        /// <summary>
        /// Is this event reskins normal tiles to event-specific tile.
        /// </summary>
        ///
        int SortOrder { get; }
        bool IsNormalTileReskinGameEventType { get; }
        TileKinds NormalTileSelectedForReskinOnThisLevel { get; set; }
        GameEventBase ActiveGameEvent { get; }
        string ActiveGameEventUid { get; }
        int LastCollectedScores { get; }
        int CollectedScores { get; }
        int ScoresThatWouldBeLost { get; }
        bool WillCompetitionProgressBeLost { get; }
        bool IsAlternateMapScreen { get; }
        void Init(IContext context);
        void Setup(ILevel level);
        void ProcessOnLevelWin();
        void ProcessOnLevelLose();
        void HandleDebugWin();
        void AddGameEventScore(int score);
        void SubmitScore(int score, bool append = false);
        void FinalizeScore();
        GameEventGameplayType GetGameplayType();
        event Action OnSetupEvent;
        void ProcessOnExit();
        void ProcessOnShuffleFailed();
        bool MakeScoresPenalizable();
        void ProcessOnFirstMove();
    }
}