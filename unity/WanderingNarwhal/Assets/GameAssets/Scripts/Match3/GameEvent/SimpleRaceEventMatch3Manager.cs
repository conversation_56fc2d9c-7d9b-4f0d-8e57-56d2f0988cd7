using System.Collections.Generic;
using BBB.DI;
using BBB.RaceEvents;

namespace BBB
{
    public class SimpleRaceEventMatch3Manager : IRaceEventMatch3Manager, IContextInitializable
    {
        private IRaceEventManager _raceEventManager;

        private readonly List<RaceEvent> _activeRaceEvents = new ();
        private bool _isStartedLevelPlay;
        
        public void InitializeByContext(IContext context)
        {
            _raceEventManager = context.Resolve<IRaceEventManager>();
        }

        public void Setup(ILevel level)
        {
            _isStartedLevelPlay = false;
            UpdateActiveGameEvents();
        }

        private void UpdateActiveGameEvents()
        {
            _activeRaceEvents.Clear();
            foreach (var raceEvent in _raceEventManager.GetAllEvents())
            {
                if (raceEvent.Joined)
                {
                    _activeRaceEvents.Add(raceEvent);
                }
            }
        }

        public bool IsAnyRaceEventOfType(RaceEventTypes.RaceEventType type, out string eventUid)
        {
            foreach (var raceEvent in _activeRaceEvents)
            {
                if (raceEvent.RaceEventType == type)
                {
                    eventUid = raceEvent.Uid;
                    return true;
                }
            }

            eventUid = null;
            return false;
        }

        public void AddScore(int score, bool append = false)
        {
            foreach (var raceEvent in _activeRaceEvents)
            {
                if (raceEvent.RaceEventType == RaceEventTypes.RaceEventType.Collect)
                {
                    raceEvent.AddScore(raceEvent.IsMultiplierScoreStreakActive() ? score * raceEvent.GetScoreMultiplier(true) : score, append);
                }
            }
        }

        public void ProcessOnFirstMove()
        {
            _isStartedLevelPlay = true;
            foreach (var raceEvent in _activeRaceEvents)
            {
                raceEvent.IncrementFailedAttempt();
            }
        }

        public int GetCollectEventScore()
        {
            foreach (var raceEvent in _activeRaceEvents)
            {
                if (raceEvent.RaceEventType == RaceEventTypes.RaceEventType.Collect)
                {
                    return raceEvent.LastAddedScoreDeltaToShow;
                }
            }
            return 0;
        }
        

        public void HandleDebugWin()
        {
        
        }

        public void ProcessOnLevelWin()
        {
            HandleWin();
        }

        public void ProcessOnExit(bool isStartedLevelPlay)
        {
            ProcessFailure(isStartedLevelPlay);
        }
        
        public void ProcessOnShuffleFailed()
        {
            foreach (var raceEvent in _activeRaceEvents)
            {
                if (raceEvent.IsLaunched())
                {
                    raceEvent.RestorePenalizableScore();
                    raceEvent.DecrementFailedAttempt(isShuffleFailed: true);
                }
            }
        }

        public void ProcessOnLevelLose()
        {
            ProcessFailure(true);
        }

        public bool MakeEventsPenalizable()
        {
            bool anyMade = false;
            foreach (var raceEvent in _activeRaceEvents)
            {
                if(raceEvent.IsLaunched())
                    if (raceEvent.CacheToPenalizableScore())
                    {
                        anyMade = true;
                    }
            }

            return anyMade;
        }

        public bool IsAnyEventStreakBroken()
        {
            foreach (var raceEvent in _activeRaceEvents)
            {
                if (raceEvent.RaceEventType == RaceEventTypes.RaceEventType.Streak && raceEvent.GetOwnScore() >= 1)
                {
                    return true;
                }
            }
            return false;
        }

        public bool IsAnyEventDoubleScoreActive()
        {
            foreach (var raceEvent in _activeRaceEvents)
            {
                if (raceEvent.IsLaunched() && raceEvent.Joined && raceEvent.IsMultiplierScoreStreakActive())
                    return true;
            }
            return false;
        }
        
        public int GetHighestScoreMultiplier(bool includeCurrent)
        {
            int highestMultiplier = int.MinValue;
            foreach (var raceEvent in _activeRaceEvents)
            {
                if (raceEvent.IsLaunched() && raceEvent.Joined &&
                    raceEvent.IsMultiplierScoreStreakActive())
                {
                    var curMultiplier = raceEvent.GetScoreMultiplier(includeCurrent);
                    if (curMultiplier > highestMultiplier)
                    {
                        highestMultiplier = curMultiplier;
                    }
                }
            }

            if (highestMultiplier != int.MinValue)
            {
                return highestMultiplier;
            }
            
            return 1;
        }

        private void ProcessFailure(bool isStartedLevelPlay)
        {
            foreach (var raceEvent in _activeRaceEvents)
            {
                if (raceEvent.IsLaunched())
                {
                    raceEvent.RestorePenalizableScore();

                    if (isStartedLevelPlay)
                    {
                        _raceEventManager.ClearLastDeltaScores();
                        
                        if (raceEvent.ResetScore())
                        {
                            raceEvent.SubmitScore(null);
                        }
                    }
                }
            }
        }

        private void HandleWin()
        {
            foreach (var raceEvent in _activeRaceEvents)
            {
                if (raceEvent.IsLaunched() && raceEvent.Joined)
                {
                    raceEvent.RestorePenalizableScore();
                    if (_isStartedLevelPlay)
                        raceEvent.DecrementFailedAttempt();
                    if (raceEvent.RaceEventType != RaceEventTypes.RaceEventType.Collect)
                    {
                        raceEvent.AddScore(raceEvent.IsMultiplierScoreStreakActive() ? raceEvent.GetScoreMultiplier() : 1);
                    }
                    raceEvent.SubmitScore(null);
                }
                
            }
        }
    }
}