namespace BBB
{
    public interface IRoyaleEventMatch3Manager
    {
        bool ShouldActivateBackground { get; }
        void HandleDebugWin();
        void ProcessOnLevelWin();
        void Setup(ILevel level);
        void ProcessOnExit(bool isStartedLevelPlay);
        void ProcessOnLevelLose();
        bool MakeEventsPenalizable();
        int EventScoreThatWillBeLost();
        void RestorePenalizableScore();
    }
}