#if UNITY_EDITOR
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using BBB.DI;
using BBB.GameAssets.Scripts.Player;
using BBB.Map;
using BBB.Match3.Systems;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.M3Editor
{
    public class MinimumSearchTrainingSystem : IContextInitializable, ITrainingSystem
    {
        private static readonly List<string> TrainWithLevelsOnly = new List<string>
        {
            "level8",
            "level19"
        };
        
        private M3EditorProgressPopup _progressPopup;
        private AutoBruteSystem _autoBruteSystem;
        private IConfig _config;
        private SpawnerSettingsManager _spawnerSettingsManager;

        public void InitializeByContext(IContext context)
        {
            _progressPopup = context.Resolve<M3EditorProgressPopup>();
            _autoBruteSystem = context.Resolve<AutoBruteSystem>();
            _config = context.Resolve<IConfig>();
            _spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
        }

        public IEnumerator TrainRoutine()
        {
            var trainingData = AiDataManagement.LoadLevelsTrainingData("AiTrainingData");
            var aiState = AiDataManagement.LoadAiState(AiTrainingSettings.AiStateAssetName);
            foreach (var kvp in trainingData)
            {
                Debug.Log("Starting training with:");
                Debug.Log(kvp.Key + ": \n" + kvp.Value);
                Debug.Log(kvp.Value);
            }

            var levelConfigs = _config.Get<FBConfig.ProgressionLevelConfig>();
            var levelDict = TrainWithLevelsOnly.ToDictionary(uid => uid, uid => levelConfigs[uid].LoadLevel(0, _spawnerSettingsManager.SpawnerSettings));
            var allLevelGoals = new List<GoalType>(3);
            foreach (var level in levelDict.Values)
            {
                foreach (var goalKvp in level.Goals)
                {
                    if (goalKvp.Value > 0)
                    {
                        var goal = goalKvp.Key;
                        if (!allLevelGoals.Contains(goal))
                        {
                            allLevelGoals.Add(goal);
                        }
                    }
                }
            }

            var consequencesToTrain = new Queue<ConsequenceType>();
            foreach (var consequenceType in allLevelGoals.SelectMany(AiHelper.GetConsequencesForGoal))
            {
                Debug.Log("Training for " + consequenceType);
                consequencesToTrain.Enqueue(consequenceType);
            }

            _progressPopup.SetProcessTitle("Training on levels");
            while (consequencesToTrain.Count > 0)
            {
                var curConsequence = consequencesToTrain.Peek();

                float minCoef = float.MaxValue;
                float minParam = float.NaN;

                for (float val = AiTrainingSettings.StartingPoint; val <= AiTrainingSettings.EndPoint; val += AiTrainingSettings.StepLength)
                {
                    aiState.Replace(curConsequence, val);

                    var aiResultsData = new Dictionary<string, Match3Statistics>();
                    var filteredTrainingData = new Dictionary<string, Match3Statistics>();
                    var levelsCount = TrainWithLevelsOnly.Count;
                    var levelIndex = 0;

                    foreach (var levelUid in TrainWithLevelsOnly)
                    {
                        _progressPopup.ShowFirstBar($"Training in level: {levelUid}", levelIndex / (float)levelsCount);

                        var resultStats = new Match3Statistics(PickLogic.Heuristic);
                        yield return CollectAiStatsForLevel(levelDict[levelUid], resultStats);
                        aiResultsData.Add(levelUid, resultStats);
                        filteredTrainingData.Add(levelUid, trainingData[levelUid]);
                        levelIndex++;
                    }

                    //var resultTypes = AiHelper.GetGoalForConsequence(curConsequence);
                    var comparison = CompareResultsToTrainingData(allLevelGoals.Select(r => r.ToString()).ToList(), aiResultsData, filteredTrainingData);

                    Debug.Log($"For {curConsequence}={val} comparison coef = " + comparison);

                    if (comparison < minCoef)
                    {
                        minCoef = comparison;
                        minParam = val;
                    }
                }

                Debug.Log($"Min coef refreshed to {minCoef} for argument {minParam} of {curConsequence}");

                aiState.Replace(curConsequence, minParam);

                consequencesToTrain.Dequeue();
                Debug.Log($"Rolling to {consequencesToTrain.Peek()}, {consequencesToTrain.Count} trainings left in total");

                AiDataManagement.SaveAiState(aiState, AiTrainingSettings.AiStateAssetName);
            }

            yield return null;
            _progressPopup.Hide();
        }

        private float CompareResultsToTrainingData(IList<string> resultTypes, 
                                                   IDictionary<string, Match3Statistics> resultsData,
                                                   IDictionary<string, Match3Statistics> trainingData)
        {
            var comparisonResults = new List<float>();

            foreach (var levelUid in resultsData.Keys)
            {
                var levelAiResults = resultsData[levelUid];
                var levelTrainingResults = trainingData[levelUid];

                var ratios = Match3Statistics.CompareByAverage(resultTypes, levelAiResults, levelTrainingResults);
                comparisonResults.AddRange(ratios);
            }

            var sqrSum = comparisonResults.Aggregate(0f, (a, b) => a + b * b);
            return Mathf.Sqrt(sqrSum / comparisonResults.Count);
        }

        private IEnumerator CollectAiStatsForLevel(Level level, Match3Statistics outStats)
        {
            var autoBruteParams = new AutoBruteParams
            {
                Limit = LimitType.Turns,
                PickLogic = PickLogic.Heuristic,
                Runs = AiTrainingSettings.RunsPerStep,
                LimitNumber = level.TurnsLimit,
                TestName =level.Config.Uid,
                ShowBruteProgress = true,
            };

            yield return _autoBruteSystem.GatherStatistics(level, autoBruteParams, new bool[1], 
                (stats, code, message) =>
                {
                    outStats.Merge(stats);
                });
        }
    }
}
#endif