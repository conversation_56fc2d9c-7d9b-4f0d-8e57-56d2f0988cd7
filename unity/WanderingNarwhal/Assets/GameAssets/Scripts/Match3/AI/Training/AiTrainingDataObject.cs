using System;
using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.M3Editor
{
    public class AiTrainingDataObject : ScriptableObject
    {
        [Serializable]
        public class LevelInstance
        {
            public string LevelUid;
            public List<Pair> Data;
        }

        [Serializable]
        public class Pair
        {
            /// <summary>
            /// GoalType enum value.
            /// </summary>
            [GoalTypeName]
            public long Type;

            public int Count;
        }

        public List<LevelInstance> GoalData = new List<LevelInstance>();
    }
}