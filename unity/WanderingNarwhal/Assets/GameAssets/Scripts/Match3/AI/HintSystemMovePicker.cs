using System.Collections.Generic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems
{
    public static class HintSystemMovePicker
    {
        public static bool GetBestPossibleMoves(ref HashSet<PossibleMove> bestMoves, ref bool shouldHighlight,
            ILevel level, Grid grid,
            GoalsSystem goalSystem,
            M3SpawnSystem spawnSystem, GameController gameController, TileResourceSelector tileResources)
        {
            var possibleMoves = SearchMatchesSystem.SearchForAllPossibleMoves(grid);
            if (possibleMoves.Count == 0)
            {
                shouldHighlight = false;
                return false;
            }

            shouldHighlight = true;
            // If bolt can be generated by some move (and this is not the last move) then select it without any doubts.
            if (gameController.RemainingMoves > 1)
            {
                foreach (var m in possibleMoves)
                {
                    if (m.Type == PossibleMoveType.DoubleTap) continue;
                    var longestLen = m.GetLongestMatchLength();
                    if (longestLen >= 5)
                    {
                        bestMoves.Add(m);
                    }
                }
            }

            if (bestMoves.Count > 0)
                return true;

            // Try select bolt+bolt combo move.
            var isColorBombExists = false;
            foreach (var m in possibleMoves)
            {
                if (m.Type == PossibleMoveType.DoubleTap) continue;
                var firstCellSpeciality = m.FirstCell.Tile?.Speciality;
                var secondCellSpeciality = m.SecondCell.Tile?.Speciality;

                if (firstCellSpeciality == TileSpeciality.ColorBomb && secondCellSpeciality == TileSpeciality.ColorBomb)
                {
                    bestMoves.Add(m);
                }

                isColorBombExists |= firstCellSpeciality == TileSpeciality.ColorBomb ||
                                     secondCellSpeciality == TileSpeciality.ColorBomb;
            }

            if (bestMoves.Count > 0)
                return true;

            if (isColorBombExists)
            {
                // Try select bolt+booster combo move.
                foreach (var m in possibleMoves)
                {
                    if (m.Type == PossibleMoveType.DoubleTap) continue;
                    var firstCellSpeciality = m.FirstCell.Tile?.Speciality ?? TileSpeciality.None;
                    var secondCellSpeciality = m.SecondCell.Tile?.Speciality ?? TileSpeciality.None;

                    if (firstCellSpeciality == TileSpeciality.ColorBomb && secondCellSpeciality.IsRegularBoost() ||
                        secondCellSpeciality == TileSpeciality.ColorBomb && firstCellSpeciality.IsRegularBoost())
                    {
                        bestMoves.Add(m);
                    }
                }

                if (bestMoves.Count > 0)
                    return true;
            }

            // Try select booster+booster move.
            foreach (var m in possibleMoves)
            {
                if (m.Type == PossibleMoveType.DoubleTap) continue;
                var firstCellSpeciality = m.FirstCell.Tile?.Speciality ?? TileSpeciality.None;
                var secondCellSpeciality = m.SecondCell.Tile?.Speciality ?? TileSpeciality.None;
                if (firstCellSpeciality.IsRegularBoost() && secondCellSpeciality.IsRegularBoost())
                {
                    bestMoves.Add(m);
                }
            }

            if (bestMoves.Count > 0)
                return true;


            if (gameController.RemainingMoves > 1)
            {
                // If a bomb can be generated by some move (and this is not the last move) then select it without any doubts.
                foreach (var m in possibleMoves)
                {
                    if (m.Type == PossibleMoveType.DoubleTap) continue;
                    var totalMatchesLength = m.GetMaxEqualMatchesLength();
                    if (totalMatchesLength >= 5)
                    {
                        bestMoves.Add(m);
                    }
                }

                // If a line-breaker/Propeller can be generated by some move (and this is not the last move) then use this
                foreach (var m in possibleMoves)
                {
                    if (m.Type == PossibleMoveType.DoubleTap) continue;
                    var longestLen = m.GetMaxEqualMatchesLength();
                    if (longestLen >= 4)
                    {
                        bestMoves.Add(m);
                    }
                }

                if (bestMoves.Count > 0)
                    return true;
            }

            var simpleTileMove = SelectBestSimpleTileMove(level, grid, goalSystem, spawnSystem,
                gameController,
                tileResources);
            if (simpleTileMove.Equals(default))
                return false;
            if (simpleTileMove.GetMaxEqualMatchesLength() <= 3)
                shouldHighlight = false;
            bestMoves.Add(simpleTileMove);
            return true;
        }


        private static PossibleMove SelectBestSimpleTileMove(ILevel level, Grid grid, GoalsSystem goalsSystem,
            M3SpawnSystem spawnSystem, GameController gameController, TileResourceSelector tileResources)
        {
            return UsefulMovesPicker.GetBestPossibleMove(level, grid, goalsSystem, spawnSystem, gameController,
            tileResources, true);
        }
    }
}

    
