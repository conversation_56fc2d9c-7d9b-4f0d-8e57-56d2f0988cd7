using System;
using System.Collections.Generic;
using BBB.GameAssets.Scripts.Player;
using UnityEngine;
using BBB.Map;


namespace BBB.Match3.Systems
{
    public class BalanceReportConfigObject : ScriptableObject
    {
        public int Runs = 200;
        public LimitType Limit = LimitType.Goal;
        public Stage Stage = Stage.Good;
        public PickLogic PickLogic = PickLogic.Heuristic;
        public string LevelsToUse;
        public float EasyThreshold = 0.25f;
        public float MediumThreshold = 0.5f;
        public float HardThreshold = 0.75f;

        public IEnumerable<string> GetLevelsToUse(IDictionary<string,FBConfig.ProgressionLevelConfig> levelConfigs)
        {
            var commaSplitArray = LevelsToUse.Split(',');

            foreach (var commaSplitElement in commaSplitArray)
            {
                if (commaSplitElement.Contains("-"))
                {
                    var dashSplitArray = commaSplitElement.Split('-');
                    if (dashSplitArray.Length != 2)
                    {
                        UnityEngine.Debug.LogError("Format error: more then one dash in a row");
                        continue;
                    }

                    var dashSplitStart = dashSplitArray[0];
                    var dashSplitEnd = dashSplitArray[1];

                    var locationNameStart = string.Empty;
                    var locationNameEnd = string.Empty;
                    var numberStart = string.Empty;
                    var numberEnd = string.Empty;
                    
                    foreach(char ch in dashSplitStart)
                        if (Char.IsNumber(ch))
                            numberStart += ch;
                        else
                            locationNameStart += ch;
                    
                    foreach(var ch in dashSplitEnd)
                        if (char.IsNumber(ch))
                        {
                            numberEnd += ch;
                        }
                        else
                        {
                            locationNameEnd += ch;
                        }
                    
                    if (locationNameStart != locationNameEnd)
                    {
                        UnityEngine.Debug.LogError($"Format error: dash split locations {locationNameStart} {locationNameEnd} are not the same");
                        continue;
                    }

                    if (int.TryParse(numberStart, out var start) && int.TryParse(numberEnd, out var end))
                    {
                        for (var i = start; i <= end; i++)
                            yield return locationNameStart + i;
                    }
                    else
                    {
                        UnityEngine.Debug.LogError($"Format error: one of dash split locations {locationNameStart} {locationNameEnd} does not have numbers specified");
                    }
                    
                }
                else if(CheckIfCommaSplitElementContainsNumber(commaSplitElement))
                {
                    yield return commaSplitElement;
                }
                else
                {
                    foreach (var levelConfig in levelConfigs.GetLocationLevels(commaSplitElement))
                        yield return levelConfig.Uid;
                }
            }
        }

        private static bool CheckIfCommaSplitElementContainsNumber(string commaSplitElement)
        {
            foreach (var ch in commaSplitElement)
            {
                if (!char.IsNumber(ch)) continue;

                return true;
            }

            return false;
        }
    }
}