using System.Collections.Generic;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems
{
    public class UsefulMovesPicker
    {
        private static HashSet<Coords> _affectedGoalCoords = new();
        private static Dictionary<Coords, Tile> _tilesToRestore = new();
        private static HashSet<Coords> _goalCoords = new();
        private static MechanicTargetingManager _mechanicTargetingManager;

        public static PossibleMove GetBestPossibleMove(ILevel level, Grid grid, GoalsSystem goalsSystem,
            M3SpawnSystem spawnSystem, GameController gameController, TileResourceSelector tileResources, bool selectRandomMove = false)
        {
            var possibleMoves = SearchMatchesSystem.SearchForAllPossibleMoves(grid);
            var validMoves = new List<PossibleMove?>();

            foreach (var pm in possibleMoves)
            {
                validMoves.Add(pm);
            }

            //get goal coords for each generated grid, because it could have a new tile color goal coords
            goalsSystem.CalculateGoalRelatedCoords(grid, ref _goalCoords);
            _mechanicTargetingManager = new MechanicTargetingManager(gameController.Settings.MechanicTargetingConfigs);
            _mechanicTargetingManager.Init(grid, goalsSystem);
            
            PossibleMove? maxUsefulMove = null;
            PossibleMove? maxNotUsefulMove = null;
            var usefulMovesCount = 0;
            var notUsefulMovesCount = 0;
            foreach (var possibleMove in validMoves)
            {
                if (AssistSystem.HasAffectedGoalCoords(grid, possibleMove.Value, _mechanicTargetingManager, 
                        _goalCoords, goalsSystem, ref _affectedGoalCoords, ref _tilesToRestore) &&
                    (!maxUsefulMove.HasValue || usefulMovesCount < _affectedGoalCoords.Count))
                {
                    usefulMovesCount = _affectedGoalCoords.Count;
                    maxUsefulMove = possibleMove;
                }
                else
                {
                    var previousMatches = new List<Match>();
                    var gridWeight = 0;
                    if (possibleMove.Value.Matches != null)
                    {
                        foreach (var match in possibleMove.Value.Matches)
                        {
                            if (match.Length == 3)
                                gridWeight += 1;
                            if (match.Length == 4 && match.MatchType != MatchType.Square)
                            {
                                gridWeight += 10;
                                TileSpeciality speciality = TileSpeciality.RowBreaker;
                                if (match.GetSimplifiedDirections() == SimplifiedDirections.Vertical)
                                    speciality = TileSpeciality.ColumnBreaker;
                                if (AssistSystem.CanAffectGoalCoords(match, speciality, grid, _goalCoords))
                                {
                                    gridWeight += 5;
                                }
                            }
                            if (match.MatchType == MatchType.Square)
                            {
                                gridWeight += 7;
                            }

                            if (match.Length >= 5)
                            {
                                gridWeight += 50;
                                if (AssistSystem.CanAffectGoalCoords(match, TileSpeciality.ColorBomb, grid, _goalCoords))
                                    gridWeight += 25;
                            }

                            foreach (var previousMatch in previousMatches)
                            {
                                if (match.Length >= 3 && match.Length < 5 && match.AnyOverlap(previousMatch) && match.MatchType != MatchType.Square)
                                {
                                    gridWeight += 20;
                                    if (AssistSystem.CanAffectGoalCoords(match, TileSpeciality.Bomb, grid, _goalCoords))
                                        gridWeight += 10;
                                }
                            }
                        
                            previousMatches.Add(match);
                        }
                    }

                    if (notUsefulMovesCount <= gridWeight)
                    {
                        notUsefulMovesCount = gridWeight;
                        maxNotUsefulMove = possibleMove;
                    }
                }
            }
            
            _mechanicTargetingManager.UnInit();

            if (selectRandomMove && notUsefulMovesCount < 40)
            {
                maxNotUsefulMove = possibleMoves.DeterministicRandomInSelf();
            }
                
            return maxUsefulMove.HasValue && notUsefulMovesCount < 50
                ? maxUsefulMove.Value
                : maxNotUsefulMove.Value;
        }
    }
}