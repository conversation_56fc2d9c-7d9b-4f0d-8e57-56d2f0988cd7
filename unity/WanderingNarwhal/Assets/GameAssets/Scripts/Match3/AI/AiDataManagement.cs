#if UNITY_EDITOR
using System.Collections.Generic;
using BBB.M3Editor;
using GameAssets.Scripts.Match3.Logic;
using UnityEditor;
using UnityEngine;

namespace BBB.Match3.Systems
{
    public static class AiDataManagement
    {
        public static BalanceReportConfigObject LoadBalacingReportConfig()
        {
            var configObject = Resources.Load<BalanceReportConfigObject>("AI/BalanceReportConfig");
            return configObject;
        }

        public static ConsequenceState LoadAiState(string assetName)
        {
            var configObject = Resources.Load<AiConfigObject>("AI/" + assetName);
            var result = configObject == null ? CreateDefault() : configObject.ToConsequenceState();
            result.FillMissingWith(1f);
            return result;
        }

        public static void SaveAiState(ConsequenceState state, string assetName)
        {
            var configObject = ScriptableObject.CreateInstance<AiConfigObject>();
            configObject.Replace(state);

            var path = "Assets/GameAssets/Resources/AI/"+assetName+".asset";
            AssetDatabase.CreateAsset(configObject, path);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        public static Dictionary<string, Match3Statistics> LoadLevelsTrainingData(string assetName)
        {
            var trainingDataObject = Resources.Load<AiTrainingDataObject>("AI/" + assetName);
            var trainingDataDictionary = new Dictionary<string, Match3Statistics>();

            foreach (var instance in trainingDataObject.GoalData)
            {
                trainingDataDictionary[instance.LevelUid] = ConvertToStatisticsObject(instance.Data);
            }

            return trainingDataDictionary;
        }

        private static Match3Statistics ConvertToStatisticsObject(List<AiTrainingDataObject.Pair> list)
        {
            var result = new Match3Statistics(PickLogic.Human);

            foreach(var pair in list)
                result.Add(((GoalType)pair.Type).ToNameString(), pair.Count, true);

            return result;
        }

        public static ConsequenceState CreateDefault()
        {
            var result = new ConsequenceState();
            result.Add(ConsequenceType.ScoreGoal, 1f);
            result.Add(ConsequenceType.TileKindGoal, 1f);
            result.Add(ConsequenceType.LitterDestroy, 1f);
            result.Add(ConsequenceType.LitterGoal, 1f);
            result.Add(ConsequenceType.StickerLayerDestroy, 1f);
            result.Add(ConsequenceType.StickerGoal, 1f);
            result.Add(ConsequenceType.BackgroundGoal, 1f);
            result.Add(ConsequenceType.PetalGoal, 1f);
            result.Add(ConsequenceType.DestructibleWall, 1f);
            result.Add(ConsequenceType.IceCubeLayerDestroy, 1f);
            result.Add(ConsequenceType.IceCubeGoal, 1f);
            result.Add(ConsequenceType.ChainGoal, 1f);
            result.Add(ConsequenceType.PinataGoal, 1f);
            result.Add(ConsequenceType.PinataEndangered, 1f);
            result.Add(ConsequenceType.DropItemWentDown, 1f);
            result.Add(ConsequenceType.DropItemGoal, 1f);
            result.Add(ConsequenceType.ChainLayerDestroyed, 1f);
            result.Add(ConsequenceType.ColorBombCreated, 1f);
            result.Add(ConsequenceType.BombCreated, 0.5f);
            result.Add(ConsequenceType.LineBreakerCreated, 0.5f);
            result.Add(ConsequenceType.ColorBombUsed, 1f);
            result.Add(ConsequenceType.BombUsed, 1f);
            result.Add(ConsequenceType.LinebreakerUsed, 1f);
            result.Add(ConsequenceType.DropItemMovedOverDespawner, 1f);
            result.Add(ConsequenceType.AnimalGoal, 1f);
            result.Add(ConsequenceType.FrameDestroy, 1f);
            result.Add(ConsequenceType.SandGoal, 1f);
            result.Add(ConsequenceType.SandLayerDestroyed, 1f);
            result.Add(ConsequenceType.IvyLayerDestroyed, 1f);
            result.Add(ConsequenceType.ColorCrateLayerDestroy, 1f);
            result.Add(ConsequenceType.ColorCrateGoal, 1f);
            result.Add(ConsequenceType.WatermelonLayerDestroy, 1f);
            result.Add(ConsequenceType.WatermelonGoal, 1f);
            result.Add(ConsequenceType.VaseLayerDestroy, 1f);
            result.Add(ConsequenceType.VaseGoal, 1f);
            result.Add(ConsequenceType.MoneyBagGoal, 1f);
            result.Add(ConsequenceType.PenguinGoal, 1f);
            result.Add(ConsequenceType.EggLayerDestroy, 1f);
            result.Add(ConsequenceType.EggGoal, 1f);
            result.Add(ConsequenceType.BirdGoal, 1f);
            result.Add(ConsequenceType.BananaGoal, 1f);
            result.Add(ConsequenceType.SheepGoal, 1f);
            result.Add(ConsequenceType.SkunkGoal, 1f);
            result.Add(ConsequenceType.SquidGoal, 1f);
            result.Add(ConsequenceType.BowlingPin, 1f);
            result.Add(ConsequenceType.BushGoal, 1f);
            result.Add(ConsequenceType.SodaBottle, 1f);
            result.Add(ConsequenceType.SafeGoal, 1f);
            result.Add(ConsequenceType.FlowerPotLayerDestroy, 1f);
            result.Add(ConsequenceType.FlowerPotGoal, 1f);
            result.Add(ConsequenceType.IceBarGoal, 1f);
            result.Add(ConsequenceType.DynamiteStick, 1f);
            result.Add(ConsequenceType.GiantPinataGoal, 1f);
            result.Add(ConsequenceType.MetalBarGoal, 1f);
            result.Add(ConsequenceType.Shelf, 1f);
            result.Add(ConsequenceType.JellyFish, 1f);
            result.Add(ConsequenceType.GoldenScarab, 1f);
            result.Add(ConsequenceType.FireWorks, 1f);
            result.Add(ConsequenceType.SlotMachine, 1f);
            result.Add(ConsequenceType.Stone, 1f);
            result.Add(ConsequenceType.Pouch, 1f);
            return result;
        }
    }
}
#endif
