#if UNITY_EDITOR
using System;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems
{
    public class TileDestroyAbsoluteValuer : IConsequenceValuer
    {
        private readonly Func<Tile, int> _counter;
        private readonly float _power;
        
        public TileDestroyAbsoluteValuer(Func<Tile, int> counter)
        {
            _counter = counter;
        }
        public float GetValue(int longestMatchCount, GoalState prevProgressAchieved, GoalState curProgressAcheived,
            GoalState totalGoals,
            Grid prevGrid, Grid curGrid, Grid originalGrid)
        {
            var prevCount = Count(prevGrid);
            var curCount = Count(curGrid);

            return curCount < prevCount ? 1f : 0f;
        }

        private int Count(Grid grid)
        {
            var result = 0;
            grid.ForeachTile((tile, coords) =>
            {
                result += _counter(tile);
            });

            return result;
        }
    }
}
#endif