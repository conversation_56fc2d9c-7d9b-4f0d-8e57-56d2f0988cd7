#if UNITY_EDITOR
using System;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems
{
    public class TileAppearedValuer : IConsequenceValuer
    {
        private readonly Predicate<Tile> _tilePredicate;
        
        public TileAppearedValuer(Predicate<Tile> tilePredicate)
        {
            _tilePredicate = tilePredicate;
        }
        
        public float GetValue(int longestMatchCount, GoalState prevProgressAchieved, GoalState curProgressAcheived,
            GoalState totalGoals, Grid prevGrid,
            Grid curGrid, Grid originalGrid)
        {
            var prevCount = CountTiles(prevGrid);
            var curCount = CountTiles(curGrid);

            var mult = longestMatchCount >= 4 ? 2f : 1f;
            return mult*(curCount > prevCount ? 1f : 0f);
        }

        private int CountTiles(Grid grid)
        {
            var result = 0;
            grid.ForeachTile((tile, coords) =>
            {
                if (_tilePredicate(tile))
                    result++;
            });
            return result;
        }
    }
}
#endif