#if UNITY_EDITOR
using GameAssets.Scripts.Match3.Logic;
using System;

namespace BBB.Match3.Systems
{
    public class EndangeredWithPowerUpValuer : IConsequenceValuer
    {
        private readonly Predicate<Tile> _tilePredicate;
        
        public EndangeredWithPowerUpValuer(Predicate<Tile> tilePredicate)
        {
            _tilePredicate = tilePredicate;
        }
        
        public float GetValue(int longestMatchCount, GoalState prevProgressAchieved, GoalState curProgressAcheived,
            GoalState totalGoals,
            Grid prevGrid, Grid curGrid, Grid originalGrid)
        {
            int totalCount = 0;
            originalGrid.ForeachTile((tile, coords) =>
            {
                if (_tilePredicate(tile))
                    totalCount++;
            });

            if (totalCount == 0)
                return 0f;

            var prev = CountEndangered(prevGrid);
            var cur = CountEndangered(curGrid);

            return cur > prev ? 1f : 0f;
        }

        private int CountEndangered(Grid grid)
        {
            int result = 0;
            grid.ForeachTile((tile, coords) =>
            {
                if (_tilePredicate(tile))
                {
                    if(grid.DoesColumnContain(coords.X, someTile => someTile.Speciality == TileSpeciality.ColumnBreaker)
                    || grid.DoesRowContain(coords.Y, someTile => someTile.Speciality == TileSpeciality.RowBreaker)
                    || grid.DoesSquareContain(coords, 5, someCell => someCell.HasTile() && someCell.Tile.Speciality == TileSpeciality.Bomb))
                        result++;
                }
                
            });

            return result;
        }
    }
}
#endif