#if UNITY_EDITOR
using System;
using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems
{
    public class TileUsedValuer : IConsequenceValuer
    {
        private readonly HashSet<int> _targetTileIds = new HashSet<int>();

        private readonly Func<Tile, bool> _tileCondition;

        public TileUsedValuer(Func<Tile, bool> tileCondition)
        {
            _tileCondition = tileCondition;
        }
        
        public float GetValue(int longestMatchCount, GoalState prevProgressAchieved, GoalState curProgressAcheived,
            GoalState totalGoals, Grid prevGrid,
            Grid curGrid, Grid originalGrid)
        {
            prevGrid.ForeachTile((tile,coords) =>
            {
                if (_tileCondition(tile))
                    _targetTileIds.Add(tile.Id);
            });

            if (_targetTileIds.Count == 0)
                return 0f;

            int countLeft = 0;
            curGrid.ForeachTile((tile, coords) =>
            {
                if (_targetTileIds.Contains(tile.Id))
                    countLeft++;
            });

            var result = countLeft < _targetTileIds.Count ? 1f : 0f;
            
            _targetTileIds.Clear();

            return result;
        }
    }
}
#endif