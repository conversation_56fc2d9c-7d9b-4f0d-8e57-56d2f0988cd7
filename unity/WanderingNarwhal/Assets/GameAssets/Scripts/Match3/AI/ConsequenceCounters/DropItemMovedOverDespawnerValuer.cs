#if UNITY_EDITOR
using BBB.CellTypes;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems
{
    public class DropItemMovedOverDespawnerValuer : IConsequenceValuer
    {
        public float GetValue(int longestMatchCount, GoalState prevProgressAchieved, GoalState curProgressAcheived,
            GoalState totalGoals,
            Grid prevGrid, Grid curGrid, Grid originalGrid)
        {
            bool hasAnyDropItem = originalGrid.IsAnyTile(tile => tile.Speciality == TileSpeciality.DropItem);
            
            if (!hasAnyDropItem)
                return 0f;
            
            var prevCount = CountDropItemsOverDespawners(prevGrid);
            var curCount = CountDropItemsOverDespawners(curGrid);

            return curCount > prevCount ? 1f : 0f;
        }

        private static int CountDropItemsOverDespawners(Grid grid)
        {
            int result = 0;
            grid.ForeachTile((tile, coords) =>
            {
                if (tile.Speciality == TileSpeciality.DropItem)
                {
                    bool isOverDespawner = false;
                    for (int y = coords.Y - 1; y >= 0; y--)
                    {
                        var checkCoords = new Coords(coords.X, y);

                        if (grid.TryGetCell(checkCoords, out var checkCell))
                        {
                            if (checkCell.IsAnyOf(CellState.Despawner))
                            {
                                isOverDespawner = true;
                                break;
                            }
                        }
                        else break;
                    }

                    if (isOverDespawner)
                        result++;
                }
            });
            return result;
        }
    }
}
#endif