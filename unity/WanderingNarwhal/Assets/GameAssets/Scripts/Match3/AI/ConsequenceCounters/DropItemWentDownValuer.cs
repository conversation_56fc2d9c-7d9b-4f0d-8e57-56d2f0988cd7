#if UNITY_EDITOR
using System.Collections.Generic;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems
{
    public class DropItemWentDownValuer : IConsequenceValuer
    {
        private readonly List<int> _dropItemIds = new List<int>();
        
        public float GetValue(int longestMatchCount, GoalState prevProgressAchieved, GoalState curProgressAcheived,
            GoalState totalGoals, Grid prevGrid,
            Grid curGrid, Grid originalGrid)
        {
            originalGrid.ForeachTile((tile, coord) =>
            {
                if (tile.Speciality == TileSpeciality.DropItem)
                    _dropItemIds.Add(tile.Id);
            });

            var maxChange = 0f;

            foreach (var id in _dropItemIds)
            {
                var originalHeight = GetHeight(id, originalGrid);
                var prevHeight = GetHeight(id, prevGrid);
                var curHeight = GetHeight(id, curGrid);

                var change = (prevHeight - curHeight) / originalHeight;

                if (change > maxChange)
                    maxChange = change;
            }
            
            _dropItemIds.Clear();

            return maxChange;
        }

        private float GetHeight(int tileId, Grid grid)
        {
            var result = 0f;
            grid.ForeachTile((tile,coord) =>
            {
                if (tile.Id == tileId)
                {
                    result = coord.Y / (float) grid.Height;
                }
                
            });
            
            return result;
        }
    }
}
#endif