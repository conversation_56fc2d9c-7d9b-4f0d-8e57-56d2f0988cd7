#if UNITY_EDITOR
using GameAssets.Scripts.Match3.Logic;
using System;
using BBB.Match3.Systems.CreateSimulationSystems;

namespace BBB.Match3.Systems
{
    public class EndangeredWithPossibleMatchValuer : IConsequenceValuer
    {
        private readonly Predicate<Tile> _tilePredicate;

        public EndangeredWithPossibleMatchValuer(Predicate<Tile> tilePredicate)
        {
            _tilePredicate = tilePredicate;
        }

        public float GetValue(int longestMatchCount, GoalState prevProgressAchieved, GoalState curProgressAcheived,
            GoalState totalGoals,
            Grid prevGrid, Grid curGrid, Grid originalGrid)
        {
            int totalCount = 0;
            originalGrid.ForeachTile((tile, coords) =>
            {
                if (_tilePredicate(tile))
                    totalCount++;
            });

            if (totalCount == 0)
                return 0f;

            var prev = CountEndangered(prevGrid);
            var cur = CountEndangered(curGrid);

            return cur > prev ? 0.5f : 0f;
        }

        private int CountEndangered(Grid grid)
        {
            int result = 0;
            var possibleMoves = SearchMatchesSystem.SearchForAllSimplePossibleMoves(grid);
            grid.ForeachTile((tile, coords) =>
            {
                if (_tilePredicate(tile))
                {
                    foreach (var possibleMove in possibleMoves)
                    {
                        bool shouldBreak = false;
                        foreach (var match in possibleMove.Matches)
                        {
                            if (match.Contains(coords))
                            {
                                result++;
                                shouldBreak = true;
                                break;
                            }
                        }

                        if (shouldBreak)
                            break;
                    }
                }
            });

            return result;
        }
    }
}
#endif