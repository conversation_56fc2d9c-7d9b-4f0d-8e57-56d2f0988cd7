#if UNITY_EDITOR
using GameAssets.Scripts.Match3.Logic;
using System;
using BBB.Match3.Systems.CreateSimulationSystems;

namespace BBB.Match3.Systems
{
    public class AdjacentEndangeredWithSameKindValuer : IConsequenceValuer
    {
        private readonly Predicate<Tile> _tilePredicate;

        public AdjacentEndangeredWithSameKindValuer(Predicate<Tile> tilePredicate)
        {
            _tilePredicate = tilePredicate;
        }

        public float GetValue(int longestMatchCount, GoalState prevProgressAchieved, GoalState curProgressAcheived,
            GoalState totalGoals,
            Grid prevGrid, Grid curGrid, Grid originalGrid)
        {
            var totalCount = 0;
            originalGrid.ForeachTile((tile, _) =>
            {
                if (_tilePredicate(tile))
                {
                    totalCount++;
                }
            });

            if (totalCount == 0)
                return 0f;

            var prev = CountEndangered(prevGrid);
            var cur = CountEndangered(curGrid);

            return cur > prev ? 1f : 0f;
        }

        private int CountEndangered(Grid grid)
        {
            var result = 0;
            var possibleMoves = SearchMatchesSystem.SearchForAllSimplePossibleMoves(grid);
            grid.ForeachTile((tile, coords) =>
            {
                if (!_tilePredicate(tile)) return;
                
                foreach (var possibleMove in possibleMoves)
                {
                    var shouldBreak = false;
                    foreach (var match in possibleMove.Matches)
                    {
                        if (match.Kind != tile.Kind)
                            continue;

                        var matchCoords = match.GetAllCoords();
                        var hasAdjacentMatch = false;

                        foreach (var mc in matchCoords)
                        {
                            if (Math.Abs(coords.X - mc.X) + Math.Abs(coords.Y - mc.Y) != 1) continue;
                            
                            hasAdjacentMatch = true;
                            break;
                        }

                        if (!hasAdjacentMatch)
                        {
                            continue;
                        }
                        
                        result++;
                        shouldBreak = true;
                        break;
                    }

                    if (shouldBreak)
                        break;
                }
            });

            return result;
        }
    }
}
#endif