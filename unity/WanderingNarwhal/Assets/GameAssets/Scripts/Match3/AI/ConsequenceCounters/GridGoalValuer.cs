#if UNITY_EDITOR
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.Match3.Systems
{
    public class GridGoalValuer : IConsequenceValuer
    {
        private readonly GoalType _goalType;
        private readonly float _power;
        
        public GridGoalValuer(GoalType goalType, float power)
        {
            _goalType = goalType;
            _power = power;
        }
        public float GetValue(int longestMatchCount, GoalState prevProgressAchieved, GoalState curProgressAcheived,
            GoalState totalGoals,
            Grid prevGrid, Grid curGrid, Grid originalGrid)
        {
            var originalCount = totalGoals.GetGoalValue(_goalType);

            if (originalCount == 0)
                return 0f;

            var prevCount = prevProgressAchieved.GetGoalValue(_goalType);
            var curCount = curProgressAcheived.GetGoalValue(_goalType);

            return Mathf.Pow(curCount / (float) originalCount, _power) -
                   Mathf.Pow(prevCount / (float) originalCount, _power);

        }
    }
}
#endif