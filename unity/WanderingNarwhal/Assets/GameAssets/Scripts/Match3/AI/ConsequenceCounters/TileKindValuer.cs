#if UNITY_EDITOR
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems
{
    public class TileKindValuer : IConsequenceValuer
    {
        public float GetValue(int longestMatchCount, GoalState prevProgressAchieved, GoalState curProgressAcheived,
            GoalState             totalGoals,        Grid      prevGrid,
            Grid                  curGrid,           Grid      originalGrid)
        {
            var ratiosSum = 0f;
            var count     = 0;

            var types = GoalTypeExtensions.TileKindTypes();
            for (int i = types.Length - 1; i >= 0; i--)
            {
                var tileKindType = types[i];
                var totalKindsToGet = totalGoals.GetGoalValue(tileKindType);

                if (totalKindsToGet > 0)
                {
                    var prevKindsAchieved = prevProgressAchieved.GetGoalValue(tileKindType);
                    var curKindsAchieved  = curProgressAcheived.GetGoalValue(tileKindType);

                    var ratio = (curKindsAchieved - prevKindsAchieved) / totalKindsToGet;

                    ratiosSum += ratio;
                    count++;
                }
            }

            if (count == 0)
                return 0f;

            return ratiosSum / count;
        }
    }
}
#endif