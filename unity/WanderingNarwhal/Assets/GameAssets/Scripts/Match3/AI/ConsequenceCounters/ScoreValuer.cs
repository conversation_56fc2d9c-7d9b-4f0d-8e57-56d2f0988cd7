#if UNITY_EDITOR
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems
{
    public class ScoreValuer : IConsequenceValuer
    {
        public float GetValue(int longestMatchCount, GoalState prevProgressAchieved, GoalState curProgressAcheived,
            GoalState totalGoals, Grid prevGrid,
            Grid curGrid, Grid originalGrid)
        {
            var prevScore = prevProgressAchieved.GetGoalValue(GoalType.Score);
            var curScore = curProgressAcheived.GetGoalValue(GoalType.Score);
            var totalScore = totalGoals.GetGoalValue(GoalType.Score);

            return (curScore - prevScore) / (float)totalScore;
        }
    }
}
#endif