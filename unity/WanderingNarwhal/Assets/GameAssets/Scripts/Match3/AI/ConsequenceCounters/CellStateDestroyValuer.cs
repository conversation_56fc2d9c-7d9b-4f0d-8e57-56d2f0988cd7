#if UNITY_EDITOR
using System;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.Match3.Systems
{
    public class CellStateDestroyValuer : IConsequenceValuer
    {
        private readonly Func<Cell, int> _counter;
        private readonly float _power;
        
        public CellStateDestroyValuer(Func<Cell, int> counter, float power)
        {
            _counter = counter;
            _power = power;
        }
        public float GetValue(int longestMatchCount, GoalState prevProgressAchieved, GoalState curProgressAcheived,
            GoalState totalGoals,
            Grid prevGrid, Grid curGrid, Grid originalGrid)
        {
            var originalCount = Count(originalGrid);

            if (originalCount == 0)
                return 0f;

            var prevCount = Count(prevGrid);
            var curCount = Count(curGrid);

            return Mathf.Pow(prevCount / (float) originalCount, 1f/_power) -
                   Mathf.Pow(curCount / (float) originalCount, 1f/_power);
        }

        private int Count(Grid grid)
        {
            var result = 0;
            foreach (var cell in grid.Cells)
            {
                result += _counter(cell);
            }

            return result;
        }
    }
}
#endif