#if UNITY_EDITOR
using System.Collections.Generic;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems
{
    public interface IHeuristic
    {
        ConsequenceState ValueConsequences(PossibleMove possibleMove, GoalState prevProgressAchieved,
            GoalState curProgressAcheived, GoalState originalGoals, Grid prevGrid,
            Grid curGrid, Grid originalGrid, IEnumerable<ConsequenceType> consequenceTypesToValue);
    }
}
#endif