#if UNITY_EDITOR
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using BBB.DI;
using BBB.GameAssets.Scripts.Match3.Logic;
using BBB.M3Editor;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.Match3.Systems.Exceptions;
using BBB.Match3.Systems.GoalsService;
using BBB.UI;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems
{
    /// <summary>
    /// This class is responsible to pick a certain move with a consideration of current grid state and goals
    /// </summary>
    public class HeuristicMovePicker : IContextInitializable
    {
        private GameController _gameController;
        private Grid _originalGrid;
        private List<TileKinds> _usedKinds;
        private LevelHolder _levelHolder;
        private ILevel _level;
        private int _turnsLimit;
        private ConsequenceType[] _consequenceTypesToValue;
        private IHeuristic _heuristic;
        private ConsequenceState _targetConsequenceState;
        private ConsequenceState _bestConsequenceState;
        private M3SpawnSystem _spawnSystem;
        private TileResourceSelector _tileResources;

        private struct ScoredMove
        {
            public PossibleMove Move;
            public ConsequenceState Consequences;
        }

        public void InitializeByContext(IContext context)
        {
            _gameController = context.Resolve<GameController>();
            _spawnSystem = context.Resolve<M3SpawnSystem>().Clone();
            _heuristic = new StandardHeuristic();
            _bestConsequenceState = AiDataManagement.CreateDefault();
            _tileResources = context.Resolve<TileResourceSelector>();
            _levelHolder = context.Resolve<LevelHolder>();
            RefreshForLevel(_levelHolder.level);
        }

        public void RefreshForLevel(ILevel level)
        {
            _originalGrid = level.Grid.Clone();
            _usedKinds = level.UsedKinds;
            _level = level;
            _turnsLimit = level.TurnsLimit;
            var gridStats = new GridStats(_originalGrid);

            var nonZeroGoals = new List<GoalType>(3);
            foreach (var kvp in level.Goals)
            {
                if (kvp.Value>0)
                {
                    nonZeroGoals.Add(kvp.Key);
                }
            }

            var consequenceSet = new HashSet<ConsequenceType>();

            foreach (var goal in nonZeroGoals)
            {
                var consequences = AiHelper.GetConsequencesForGoal(goal);
                foreach (var consequence in consequences)
                {
                    consequenceSet.Add(consequence);
                }
            }

            var gridConsequences = gridStats.GetConsequences();
            foreach (var consequence in gridConsequences)
            {
                consequenceSet.Add(consequence);
            }

            _consequenceTypesToValue = new ConsequenceType[consequenceSet.Count];
            var index = 0;
            foreach (var consequence in consequenceSet)
            {
                _consequenceTypesToValue[index++] = consequence;
            }


            _targetConsequenceState = AiDataManagement.LoadAiState(AiTrainingSettings.AiStateAssetName);
        }

        /// <summary>
        /// Find possible move with maximal outcome.
        /// </summary>
        /// <param name="max">This flag is used to ask picker use special HeuristicMax algo (which means the smartest AI we can do)</param>
        /// <param name="grid">Current grid state</param>
        /// <param name="originalGoals">Original starting goals of current level</param>
        /// <param name="goalSystem">Reference to goal system</param>
        /// <exception cref="InvalidOperationException">Thrown if no moves found</exception>
        public PossibleMove GetBestPossibleMove(bool max, Grid grid, GoalState originalGoals, GoalsSystem goalSystem)
        {
            //First we search for all of the possible moves with the same algo as in other plces
            var possibleMoves = new List<PossibleMove>(SearchMatchesSystem.SearchForAllPossibleMoves(grid));

            if (possibleMoves.Count == 0)
                throw new NoPossibleMovesException();

            //find a longest match
            var maxMatchLength = int.MinValue;

            foreach (var pm in possibleMoves)
            {
                var matchLength = pm.GetLongestMatchLength();
                if (matchLength > maxMatchLength)
                {
                    maxMatchLength = matchLength;
                }
            }
            
            //if longest match is match5, AI gonna pick it with 50% chance
            //this hack is made for AI to treat match5's a bit closer to real human behaviour
            if (maxMatchLength >= 5 && UnityEngine.Random.Range(0, 1f) < 0.5f)
            {
                var filteredMoves = new List<PossibleMove>();

                foreach (var pm in possibleMoves)
                {
                    if (pm.Matches == null) continue;
                    
                    foreach (var match in pm.Matches)
                    {
                        if (match.Length != maxMatchLength) continue;
                        
                        filteredMoves.Add(pm);
                        break;
                    }
                }

                possibleMoves = filteredMoves;
            }

            //take 10 random moves from possible ones for performance reasons
            //this is also what humans do, because they usually don't analyze every single move they have
            possibleMoves.Shuffle();
            var randomPossibleMoves = new List<PossibleMove>();
            var count = 0;

            foreach (var move in possibleMoves)
            {
                randomPossibleMoves.Add(move);
                count++;
                if (count >= 10)
                {
                    break;
                }
            }

            //clone achieved progress instance and grid state
            var prevProgressAchieved = goalSystem.ProgressAchieved.Clone();
            var prevGrid = grid.Clone();

            var scoredMoves = new ConcurrentBag<ScoredMove>();

            foreach (var possibleMove in randomPossibleMoves)
            {
                try
                {
                    var goalSystemClone = goalSystem.Clone();
                    var curGrid = grid.Clone();
                    var playerInput = SearchMatchesSystem.ToPlayerInput(possibleMove);

                    _spawnSystem.Setup(_level);
                    var simParams = new SimulationInputParams
                    {
                        UsedKinds = _usedKinds,
                        Settings = _gameController.Settings,
                        SimulateLoopException = _gameController.SimulateLoopException,
                        TurnsLimit = _turnsLimit,
                    };

                    //for every possible move we apply the input to current grid
                    new GravitySystem(simParams, goalSystemClone, _tileResources, null, null).CreateSimulationSync(
                        grid: curGrid,
                        playerInput: playerInput,
                        remainingMoves: 666,
                        assistParams: null,
                        spawnSystem: _spawnSystem);

                    //then get new state of acheived progress
                    var curProgressAchieved = goalSystemClone.ProgressAchieved;

                    //and value the consequences with a certain IHeuristic implementation
                    var consequences = _heuristic.ValueConsequences(possibleMove, prevProgressAchieved,
                        curProgressAchieved, originalGoals,
                        prevGrid, curGrid, _originalGrid, _consequenceTypesToValue);

                    //save consequences of every move 
                    scoredMoves.Add(new ScoredMove {Move = possibleMove, Consequences = consequences});
                }
                catch (Exception e)
                {
                    var logWasDisabled = !UnityEngine.Debug.unityLogger.logEnabled;

                    if (logWasDisabled)
                    {
                        UnityEngine.Debug.unityLogger.logEnabled = true;
                    }

                    UnityEngine.Debug.LogError("Exception " + e);

                    if (logWasDisabled)
                    {
                        UnityEngine.Debug.unityLogger.logEnabled = false;
                    }
                }
            }

            //for every consequence type to value
            foreach (var consequenceType in _consequenceTypesToValue)
            {
                if (scoredMoves.Count == 0)
                    continue;

                //we find the consequence with this type and max value
                var consequenceValueMax = float.MinValue;

                foreach (var sm in scoredMoves)
                {
                    var consequenceValue = sm.Consequences.Get(consequenceType);
                    if (consequenceValue > consequenceValueMax)
                    {
                        consequenceValueMax = consequenceValue;
                    }
                }

                var accumulatedValues = string.Empty;
                foreach (var sm in scoredMoves)
                {
                    //here we are trying to normalize consequence values by dividing it to the max one
                    //if max is zero, then replace all with 0
                    if (Math.Abs(consequenceValueMax) < 0.01f)
                    {
                        sm.Consequences.Replace(consequenceType, 0f);
                    }
                    else
                    {
                        //norialization division
                        var scaledValue = sm.Consequences.Get(consequenceType) / consequenceValueMax;
                        sm.Consequences.Replace(consequenceType, scaledValue);
                        accumulatedValues += scaledValue.ToString("F2") + " ";
                    }
                }

                if (Math.Abs(consequenceValueMax) < 0.01f)
                {
                    UnityEngine.Debug.LogWarning($"For consequence {consequenceType} the max is 0");
                }
                else
                {
                    UnityEngine.Debug.LogWarning($"For consequence {consequenceType} the values are: " + accumulatedValues);       
                }
            }

            var minSqrDistance = float.MaxValue;
            PossibleMove? selectedMove = null;

            //pick the consequence point state to select a certain move
            var targetConsequenceState = max ? _bestConsequenceState : _targetConsequenceState;

            foreach (var scoredMove in scoredMoves)
            {
                //pick a move that is closest to consequence point state in [scoreMoves.Count]-dimensional space
                var sqrDistance = ConsequenceState.SqrDistanceBetween(targetConsequenceState, scoredMove.Consequences);
                if (sqrDistance < minSqrDistance)
                {
                    minSqrDistance = sqrDistance;
                    selectedMove = scoredMove.Move;
                }
            }

            if (selectedMove.HasValue)
                return selectedMove.Value;

            throw new NoPossibleMovesException();
        }
    }
}
#endif