#if UNITY_EDITOR
using System.Collections.Generic;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems
{
    public class StandardHeuristic : IHeuristic
    {
        //each valuer returns a value from 0 to 1 that determines how much this particular delta between goal states or grid states
        //is valued; some of them have power argument that changes a curve of some [0;1]-defined function to make
        //AI value some goals more in the end of the match then in the middle (check the internal code of a valuer with power)
        private static Dictionary<ConsequenceType, IConsequenceValuer> ConsequenceValuers =
            new Dictionary<ConsequenceType, IConsequenceValuer>
            {
                { ConsequenceType.ScoreGoal, new ScoreValuer() },
                { ConsequenceType.TileKindGoal, new TileKindValuer() },
                { ConsequenceType.LitterDestroy, new BlockerDestroyValuer(AiHelper.LitterCount,3f) },
                { ConsequenceType.FrameDestroy, new BlockerDestroyValuer(AiHelper.FrameCount,3f) },
                { ConsequenceType.LitterGoal, new GridGoalValuer(GoalType.Litters, 3f) },
                { ConsequenceType.StickerLayerDestroy, new BlockerDestroyValuer(AiHelper.StickerLayerCount,3f) },
                { ConsequenceType.ColorCrateEndangered, new AdjacentEndangeredWithSameKindValuer(AiHelper.IsColorCrate) },
                { ConsequenceType.ColorCrateLayerDestroy, new BlockerDestroyValuer(AiHelper.ColorCrateCount,3f) },
                { ConsequenceType.ColorCrateEndangeredWithPowerUp, new EndangeredWithPowerUpValuer(AiHelper.IsColorCrate) },
                { ConsequenceType.StickerGoal, new GridGoalValuer(GoalType.Stickers, 3f) },
                { ConsequenceType.ColorCrateGoal, new GridGoalValuer(GoalType.ColorCrate, 3f) },
                { ConsequenceType.WatermelonLayerDestroy, new BlockerDestroyValuer(AiHelper.WatermelonLayerCount, 3f) },
                { ConsequenceType.WatermelonGoal, new GridGoalValuer(GoalType.Watermelon, 3f) },
                { ConsequenceType.BackgroundGoal, new GridGoalValuer(GoalType.Backgrounds, 3f) },
                { ConsequenceType.PetalGoal, new GridGoalValuer(GoalType.Petal, 3f) },
                { ConsequenceType.DestructibleWall, new GridGoalValuer(GoalType.DestructibleWall, 3f) },
                { ConsequenceType.IvyLayerDestroyed, new CellStateDestroyValuer(AiHelper.IvyLayerCounter, 3f) },
                { ConsequenceType.IceCubeLayerDestroy, new BlockerDestroyValuer(AiHelper.IceCubeLayerCount,3f) },
                { ConsequenceType.IceCubeGoal, new GridGoalValuer(GoalType.IceCubes, 3f) },
                { ConsequenceType.ChainGoal, new GridGoalValuer(GoalType.Chains, 3f) },
                { ConsequenceType.VaseLayerDestroy, new BlockerDestroyValuer(AiHelper.VaseLayerCount, 3f) },
                { ConsequenceType.VaseGoal, new GridGoalValuer(GoalType.Vase, 3f) },
                { ConsequenceType.MoneyBagGoal, new GridGoalValuer(GoalType.MoneyBag, 3f) },
                { ConsequenceType.PenguinGoal, new GridGoalValuer(GoalType.Penguin, 3f) },
                { ConsequenceType.EggLayerDestroy, new BlockerDestroyValuer(AiHelper.EggLayerCount, 3f) },
                { ConsequenceType.EggGoal, new GridGoalValuer(GoalType.Egg, 3f) },
                { ConsequenceType.BirdGoal, new GridGoalValuer(GoalType.Bird, 3f) },
                { ConsequenceType.ChickenGoal, new GridGoalValuer(GoalType.Chicken, 3f) },
                { ConsequenceType.BeeGoal, new GridGoalValuer(GoalType.Bee, 3f) },
                { ConsequenceType.MoleGoal, new GridGoalValuer(GoalType.Mole, 3f) },
                { ConsequenceType.SandGoal, new GridGoalValuer(GoalType.Sand, 3f) },
                { ConsequenceType.BananaGoal, new GridGoalValuer(GoalType.Banana, 3f) },
                { ConsequenceType.SheepGoal, new GridGoalValuer(GoalType.Sheep, 3f) },
                { ConsequenceType.SkunkGoal, new GridGoalValuer(GoalType.Skunk, 3f) },
                { ConsequenceType.FireWorks, new GridGoalValuer(GoalType.FireWorks, 3f) },
                { ConsequenceType.SlotMachine, new GridGoalValuer(GoalType.SlotMachine, 3f) },
                { ConsequenceType.SquidGoal, new GridGoalValuer(GoalType.Squid, 3f) },
                { ConsequenceType.BowlingPin, new GridGoalValuer(GoalType.BowlingPin, 3f) },
                { ConsequenceType.BushGoal, new GridGoalValuer(GoalType.Bush, 3f) },
                { ConsequenceType.SodaBottle, new GridGoalValuer(GoalType.SodaBottle, 3f) },
                { ConsequenceType.SafeGoal, new GridGoalValuer(GoalType.Safe, 3f) },
                { ConsequenceType.FlowerPotLayerDestroy, new BlockerDestroyValuer(AiHelper.FlowerPotLayerCount, 3f) },
                { ConsequenceType.FlowerPotGoal, new GridGoalValuer(GoalType.FlowerPot, 3f) },
                { ConsequenceType.IceBarGoal, new GridGoalValuer(GoalType.IceBar, 3f) },
                { ConsequenceType.DynamiteStick, new GridGoalValuer(GoalType.DynamiteStick, 3f) },
                { ConsequenceType.GiantPinataGoal, new GridGoalValuer(GoalType.GiantPinata, 3f) },
                { ConsequenceType.MetalBarGoal, new GridGoalValuer(GoalType.MetalBar, 3f) },
                { ConsequenceType.Shelf, new GridGoalValuer(GoalType.Shelf, 3f) },
                { ConsequenceType.JellyFish, new GridGoalValuer(GoalType.JellyFish, 3f) },
                { ConsequenceType.GoldenScarab, new GridGoalValuer(GoalType.GoldenScarab, 3f) },
                { ConsequenceType.Stone, new GridGoalValuer(GoalType.Stone, 3f) },
                { ConsequenceType.Pouch, new GridGoalValuer(GoalType.Pouch, 3f) },
                { ConsequenceType.SandLayerDestroyed, new BlockerDestroyValuer(AiHelper.SandCount, 3f) },
                { ConsequenceType.PinataGoal, new GridGoalValuer(GoalType.Pinata, 1f) },
                { ConsequenceType.AnimalGoal, new GridGoalValuer(GoalType.Animal, 3f) },
                { ConsequenceType.PinataEndangered, new EndangeredWithPowerUpValuer(AiHelper.IsPinata) },
                { ConsequenceType.ChainLayerDestroyed, new BlockerDestroyValuer(AiHelper.ChainLayerCount,3f) },
                { ConsequenceType.DropItemGoal, new GridGoalValuer(GoalType.DropItems, 1f) },
                { ConsequenceType.DropItemWentDown, new DropItemWentDownValuer() },
                { ConsequenceType.DropItemMovedOverDespawner, new DropItemMovedOverDespawnerValuer()},
                { ConsequenceType.ColorBombUsed, new TileUsedValuer(tile => tile.Speciality == TileSpeciality.ColorBomb)},
                { ConsequenceType.LinebreakerUsed, new TileUsedValuer(tile => tile.Speciality == TileSpeciality.RowBreaker || tile.Speciality == TileSpeciality.ColumnBreaker)},
                { ConsequenceType.BombUsed, new TileUsedValuer(tile => tile.Speciality == TileSpeciality.Bomb)},
                { ConsequenceType.PropellerUsed, new TileUsedValuer(tile => tile.Speciality == TileSpeciality.Propeller)},

                /*{ ConsequenceType.ColorBombCreated, new TileAppearedValuer(tile => tile.Speciality == TileSpeciality.ColorBomb)},
                { ConsequenceType.LineBreakerCreated, new TileAppearedValuer(tile => tile.Speciality == TileSpeciality.RowBreaker
                                                                                     || tile.Speciality == TileSpeciality.ColumnBreaker)},
                { ConsequenceType.BombCreated, new TileAppearedValuer(tile => tile.Speciality == TileSpeciality.Bomb)},*/

            };


        public ConsequenceState ValueConsequences(PossibleMove possibleMove, GoalState prevProgressAchieved,
            GoalState curProgressAcheived, GoalState originalGoals,
            Grid prevGrid, Grid curGrid, Grid originalGrid, IEnumerable<ConsequenceType> consequenceTypesToValue)
        {
            var state = new ConsequenceState();

            //for every consequence type to value we picked a valuer which gonna return a floating value based on the grid and goal states
            foreach (var consequenceType in consequenceTypesToValue)
            {
                if (ConsequenceValuers.TryGetValue(consequenceType, out var valuer))
                {
                    var longestMatchCount = possibleMove.GetLongestMatchLength();
                    var consequenceValue = valuer.GetValue(longestMatchCount, prevProgressAchieved, curProgressAcheived, originalGoals,
                        prevGrid, curGrid, originalGrid);

                    state.Add(consequenceType, consequenceValue);
                }
            }

            return state;

        }
    }
}
#endif
