
using System;
using System.Collections.Generic;
using System.Text;

namespace BBB.Match3.Systems
{
    public class ConsequenceState
    {
        private readonly Dictionary<ConsequenceType, float> _components = new Dictionary<ConsequenceType,float>();

        public IEnumerable<Tuple<ConsequenceType, float>> TuplesEnum
        {
            get
            {
                foreach(var kvp in _components)
                    yield return new Tuple<ConsequenceType, float>(kvp.Key, kvp.Value);
            }
        }

        public void Add(ConsequenceType type, float value)
        {
            _components.Add(type, value);
        }

        public bool HasKey(ConsequenceType type)
        {
            return _components.ContainsKey(type);
        }

        public float Get(ConsequenceType type)
        {
            return _components.TryGetValue(type, out var value) ? value : 0f;
        }

        public void Replace(ConsequenceType type, float value)
        {
            if (_components.TryGetValue(type, out var currentValue))
            {
                _components[type] = value;
            }
        }

        public ConsequenceState Clone()
        {
            var newState = new ConsequenceState();
            foreach(var kvp in _components)
                newState.Add(kvp.Key, kvp.Value);

            return newState;
        }

        public static float SqrDistanceBetween(ConsequenceState a, ConsequenceState b)
        {
            var sqrSum = 0f;
            foreach (ConsequenceType type in Enum.GetValues(typeof(ConsequenceType)))
            {
                a._components.TryGetValue(type, out var aValue);
                b._components.TryGetValue(type, out var bValue);

                sqrSum += (aValue - bValue) * (aValue - bValue);
            }

            return sqrSum;
        }

        public override string ToString()
        {
            var builder = new StringBuilder();

            foreach (var kvp in _components)
            {
                const string colon = " : ";
                builder.Append(kvp.Key).Append(colon).Append(kvp.Value).AppendLine();
            }

            return builder.ToString();
        }

        public void FillMissingWith(float value)
        {
            foreach (ConsequenceType type in Enum.GetValues(typeof(ConsequenceType)))
            {
                _components.TryAdd(type, value);
            }
        }
    }
}