using System;
using System.Collections.Generic;
using UnityEngine;

namespace BBB.Match3.Systems
{
    public class AiConfigObject : ScriptableObject
    {
        [Serializable]
        public class Pair
        {
            public ConsequenceType Type;
            public float Value;
        }
        
        public List<Pair> DataList = new List<Pair>();

        public void Replace(ConsequenceState state)
        {
            DataList.Clear();
            foreach (var tuple in state.TuplesEnum)
            {
                var pair = new Pair {Type = tuple.Item1, Value = tuple.Item2};
                DataList.Add(pair);
            }
        }

        public ConsequenceState ToConsequenceState()
        {
            var result = new ConsequenceState();
            foreach (var pair in DataList)
                result.Add(pair.Type, pair.Value);
            return result;
        }
    }
}