using System;
using System.Collections.Generic;
using BBB.CellTypes;
using BBB.Match3.Logic;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.SearchMatchesSystemTypes;
using BBB.Match3.Systems.GoalsService;
using GameAssets.Scripts.Match3.Logic;
using GameAssets.Scripts.Match3.Settings;

namespace BBB.Match3.Systems
{
    public static class OptimalMovePicker
    {
        private static readonly Cell[] _tempNeigbourCells = new Cell[4];
        private static readonly TileSpeciality[] _tempNeighboursSpecs = new TileSpeciality[4];

        /// <summary>
        /// Select most optimal move.
        /// </summary>
        /// <remarks>
        /// Prioritizes 5 regular tiles combo first,
        /// then any bolt move (combo first),
        /// then a move that leads to biggest gain of grid progress (evaluated by weight of grid state),
        /// and then a random move with highest length (in case if grid evaluation failed). -VK
        /// </remarks>
        public static PossibleMove GetBestPossibleMove(ILevel level, Grid grid, GoalsSystem goalSystem, M3SpawnSystem spawnSystem, GameController gameController, TileResourceSelector tileResources)
        {
            var possibleMoves = new HashSet<PossibleMove>(SearchMatchesSystem.SearchForAllPossibleMoves(grid));

            if (possibleMoves.Count == 0)
            {
                return default;
            }

            // If a bolt can be generated by some move (and this is not the last move) then select it without any doubts.
            if (gameController.RemainingMoves > 1)
            {
                foreach (var m in possibleMoves)
                {
                    if (m.Type == PossibleMoveType.DoubleTap) continue;
                    var longestLen = m.GetLongestMatchLength();
                    if (longestLen >= 5)
                    {
                        return m;
                    }
                }
            }

            // Try bolt+bolt combo.
            var isColorBombExists = false;
            foreach (var m in possibleMoves)
            {
                if (m.Type == PossibleMoveType.DoubleTap) continue;
                var firstCellSpeciality = m.FirstCell.Tile.IsNull() ? TileSpeciality.None : m.FirstCell.Tile.Speciality;
                var secondCellSpeciality = m.SecondCell.Tile.IsNull() ? TileSpeciality.None : m.SecondCell.Tile.Speciality;
                if (firstCellSpeciality == TileSpeciality.ColorBomb && secondCellSpeciality == TileSpeciality.ColorBomb)
                {
                    return m;
                }
                isColorBombExists |= firstCellSpeciality == TileSpeciality.ColorBomb || secondCellSpeciality == TileSpeciality.ColorBomb;
            }

            // Try bolt + booster/propeller combo.
            if (isColorBombExists)
            {
                foreach (var m in possibleMoves)
                {
                    if (m.Type == PossibleMoveType.DoubleTap) continue;
                    var firstCellSpeciality = m.FirstCell.Tile.IsNull() ? TileSpeciality.None : m.FirstCell.Tile.Speciality;
                    var secondCellSpeciality = m.SecondCell.Tile.IsNull() ? TileSpeciality.None : m.SecondCell.Tile.Speciality;
                    if ((firstCellSpeciality == TileSpeciality.ColorBomb && IsRegularBoosterOrPropeller(secondCellSpeciality)) ||
                        (secondCellSpeciality == TileSpeciality.ColorBomb && IsRegularBoosterOrPropeller(firstCellSpeciality)))
                    {
                        return m;
                    }
                }
            }

            // If a bomb can be generated by some move (and this is not the last move) then select it without any doubts.
            if (gameController.RemainingMoves > 1)
            {
                foreach (var m in possibleMoves)
                {
                    if (m.Type == PossibleMoveType.DoubleTap) continue;
                    var totalMatchesLength = m.GetMaxEqualMatchesLength();
                    if (totalMatchesLength >= 5)
                    {
                        return m;
                    }
                }
            }

            // Try bomb+bomb combo.
            foreach (var m in possibleMoves)
            {
                if (m.Type == PossibleMoveType.DoubleTap) continue;
                var firstCellSpeciality = m.FirstCell.Tile.IsNull() ? TileSpeciality.None : m.FirstCell.Tile.Speciality;
                var secondCellSpeciality = m.SecondCell.Tile.IsNull() ? TileSpeciality.None : m.SecondCell.Tile.Speciality;
                if (firstCellSpeciality == TileSpeciality.Bomb && secondCellSpeciality == TileSpeciality.Bomb)
                {
                    return m;
                }
            }
            
            // Try select propeller+propeller combo move.
            foreach (var m in possibleMoves)
            {
                if (m.Type == PossibleMoveType.DoubleTap) continue;
                var firstCellSpeciality = m.FirstCell.Tile.IsNull() ? TileSpeciality.None : m.FirstCell.Tile.Speciality;
                var secondCellSpeciality = m.SecondCell.Tile.IsNull() ? TileSpeciality.None : m.SecondCell.Tile.Speciality;
                if (firstCellSpeciality == TileSpeciality.Propeller && secondCellSpeciality == TileSpeciality.Propeller)
                {
                    return m;
                }
            }
            
            // Try select propeller+booster move.
            foreach (var m in possibleMoves)
            {
                if (m.Type == PossibleMoveType.DoubleTap) continue;
                var firstCellSpeciality = m.FirstCell.Tile.IsNull() ? TileSpeciality.None : m.FirstCell.Tile.Speciality;
                var secondCellSpeciality = m.SecondCell.Tile.IsNull() ? TileSpeciality.None : m.SecondCell.Tile.Speciality;
                if ((firstCellSpeciality == TileSpeciality.Propeller && IsRegularBooster(secondCellSpeciality)) || 
                   (secondCellSpeciality == TileSpeciality.Propeller && IsRegularBooster(firstCellSpeciality)))
                {
                    return m;
                }
            }
            
            // Try select line-breakers combo move.
            foreach (var m in possibleMoves)
            {
                if (m.Type == PossibleMoveType.DoubleTap) continue;
                var firstCellSpeciality = m.FirstCell.Tile.IsNull() ? TileSpeciality.None : m.FirstCell.Tile.Speciality;
                var secondCellSpeciality = m.SecondCell.Tile.IsNull() ? TileSpeciality.None : m.SecondCell.Tile.Speciality;
                if (IsLineBreaker(firstCellSpeciality) && IsLineBreaker(secondCellSpeciality))
                {
                    return m;
                }
            }
            
            // Try select linebreaker+bomb combo move.
            foreach (var m in possibleMoves)
            {
                if (m.Type == PossibleMoveType.DoubleTap) continue;
                var firstCellSpeciality = m.FirstCell.Tile.IsNull() ? TileSpeciality.None : m.FirstCell.Tile.Speciality;
                var secondCellSpeciality = m.SecondCell.Tile.IsNull() ? TileSpeciality.None : m.SecondCell.Tile.Speciality;
                if (IsRegularBooster(firstCellSpeciality) && IsRegularBooster(secondCellSpeciality))
                {
                    return m;
                }
            }

            // Try to double tap single color-bomb
            if (isColorBombExists)
            {
                foreach (var m in possibleMoves)
                {
                    var firstCellSpeciality = m.FirstCell.Tile.IsNull() ? TileSpeciality.None : m.FirstCell.Tile.Speciality;
                    if (m.Type == PossibleMoveType.DoubleTap && firstCellSpeciality == TileSpeciality.ColorBomb)
                    {
                        return m;
                    }
                }
            }
            
            // If a line-breaker can be generated by some move (and this is not the last move) then use this
            if (gameController.RemainingMoves > 1)
            {
                foreach (var m in possibleMoves)
                {
                    if (m.Type == PossibleMoveType.DoubleTap) continue;
                    var longestLen = m.GetLongestMatchLength();
                    if (longestLen >= 4)
                    {
                        return m;
                    }
                }
            }

            var maxGoalProgressDiff = 0f;
            var selectedMove = default(PossibleMove);

            var initialGridWeight = EvaluateGridWeight(grid);
            var initialGoalsWeight = EvaluateGoals(goalSystem);
            var initialWeight = initialGridWeight + initialGoalsWeight;

            // Simulate each move and compare grid weight difference and select best move.
            foreach (var m in possibleMoves)
            {
                try
                {
                    var goalSystemClone = goalSystem.Clone();
                    var gridClone = grid.Clone();
                    var playerInput = SearchMatchesSystem.ToPlayerInput(m);
                    spawnSystem.Setup(level);

                    var simParams = new SimulationInputParams
                    {
                        UsedKinds = level.UsedKinds,
                        Settings = gameController.Settings,
                        SimulateLoopException = gameController.SimulateLoopException,
                        TurnsLimit = 55,
                    };

                    new GravitySystem(simParams, goalSystemClone, tileResources, null, null).CreateSimulationSync(
                        grid: gridClone,
                        playerInput: playerInput,
                        remainingMoves: 55,
                        assistParams: null,
                        spawnSystem: spawnSystem);

                    if (!goalSystemClone.ProgressAchieved.AnyGoalsLeft())
                    {
                        return m;
                    }

                    var gridWeight = EvaluateGridWeight(gridClone);
                    var goalsWeight = EvaluateGoals(goalSystemClone);
                    var weight = gridWeight + goalsWeight;

                    var dif = initialWeight - weight;
                    if (selectedMove.Matches == null || dif > maxGoalProgressDiff)
                    {
                        maxGoalProgressDiff = dif;
                        selectedMove = m;
                    }
                }
                catch (Exception ex)
                {
                    UnityEngine.Debug.LogError("Move Pick Exception " + ex.Message + "\n" + ex.StackTrace);
                }
            }

            if (selectedMove.FirstCell != null && selectedMove.SecondCell != null)
            {
                return selectedMove;
            }

            // Select random move if for some reason possible moves simulation failed and couldn't select best move.
            return possibleMoves.DeterministicRandomInSelf();
        }

        private static float EvaluateGridWeight(Grid grid)
        {
            float sum = 0f;

            const int maxFreeBoostersCount = 5;
            int boostersCount = 0;
            bool isBooster = false;

            int blockedSpawners = 0;

            foreach (var cell in grid.Cells)
            {
                sum += EvaluateCellWeight(cell, grid, ref isBooster);
                if (isBooster)
                {
                    boostersCount++;
                }

                if (cell.IsAnyOf(CellState.Spawner) && cell.Tile != null)
                {
                    if (cell.Tile.IsAnyOf(TileState.ArmorMod) ||
                        cell.Tile.IsAnyOf(TileState.ZeroGravity) ||
                        !cell.Tile.IsMatchable())
                    {
                        sum += GetWeightFor("blocked_spawner");
                        blockedSpawners++;
                    }
                }
            }

            UnityEngine.Debug.Log("Blocked: "+blockedSpawners);

            if (boostersCount > maxFreeBoostersCount)
            {
                // If too much booster on grid.
                // This may happen because bot is conservative in spending boosters (due to their weights).
                sum += GetWeightFor("excess_booster");
            }

            return sum;
        }

        private static float EvaluateGoals(GoalsSystem goalsSystem)
        {
            var sum = 0f;
            var values = goalsSystem.GetGoalProgressValues();
            foreach (var kv in values)
            {
                var mlt = GetGoalWeight(kv.Key);
                // Weight is reversed, because 0 weight is bigger than 1. Higher goal progress should give less sum increase.
                sum += mlt * (1f - kv.Value);
            }

            return sum;
        }

        private static float EvaluateCellWeight(Cell cell, Grid grid, ref bool isBooster)
        {
            var sum = 0f;
            if (cell.BackgroundCount > 0)
            {
                sum += GetWeightFor("grass") * cell.BackgroundCount;
            }

            if (cell.IvyCount > 0 && cell.IsAnyOf(CellState.Ivy))
            {
                sum += GetWeightFor("ivy") * cell.IvyCount;
            }

            if (cell.Tile.IsNull())
            {
                sum += GetWeightFor("empty_cell");
            }
            else
            {
                sum += EvaluateTileWeight(cell.Tile, cell, grid, ref isBooster);
            }

            return sum;
        }

        private static bool IsRegularBooster(TileSpeciality spec)
        {
            return spec == TileSpeciality.Bomb || IsLineBreaker(spec);
        }
        
        private static bool IsRegularBoosterOrPropeller(TileSpeciality spec)
        {
            return spec is TileSpeciality.Bomb or TileSpeciality.Propeller || IsLineBreaker(spec);
        }


        private static bool IsLineBreaker(TileSpeciality spec)
        {
            return spec is TileSpeciality.RowBreaker or TileSpeciality.ColumnBreaker;
        }

        private static float EvaluateTileWeight(Tile tile, Cell cell, Grid grid, ref bool isBooster)
        {
            var sum = 0f;
            sum += GetWeightFor("TileSpeciality." + tile.Speciality);
            sum += tile.GetParam(TileParamEnum.ChainLayerCount) * GetWeightFor("chain");
            sum += tile.GetParam(TileParamEnum.IceLayerCount) * GetWeightFor("ice");
            sum += tile.GetParam(TileParamEnum.VaseLayerCount) * GetWeightFor("vase");
            sum += tile.GetParam(TileParamEnum.EggLayerCount) * GetWeightFor("egg");
            sum += tile.GetParam(TileParamEnum.FlowerPotLayerCount) * GetWeightFor("flowerpot");
            var henMlt = tile.IsAnyOf(TileState.HenMod) ? GetWeightFor("hen") : 1f;
            var monkeyMlt = tile.IsAnyOf(TileState.MonkeyMod) ? GetWeightFor("monkey") : 1f;
            sum += tile.GetParam(TileParamEnum.AdjacentHp) * GetWeightFor("adjacent_hp") * henMlt * monkeyMlt;
            sum += tile.GetParam(TileParamEnum.SquidsCount) * GetWeightFor("squid_hp");
            sum += tile.Speciality == TileSpeciality.Hive && tile.GetParam(TileParamEnum.BeeHiveOutOfBeesFlag) == 0 ? GetWeightFor("not_out_of_bees") : 0f;
            sum += tile.Speciality == TileSpeciality.MagicHat && tile.GetParam(TileParamEnum.MagicHatOutOfRabbitsFlag) == 0 ? GetWeightFor("not_out_of_rabbits") : 0f;
            sum += tile.Speciality == TileSpeciality.Shelf && tile.GetParam(TileParamEnum.AdjacentHp) == 1 ? GetWeightFor("shelf_empty") : 0f;
            sum += tile.GetParam(TileParamEnum.SizeX) * tile.GetParam(TileParamEnum.SizeY) * GetWeightFor("squid_cell_size");
            sum += tile.GetParam(TileParamEnum.SizeX) * tile.GetParam(TileParamEnum.SizeY) * GetWeightFor("bowling_cell_size");
            sum += tile.GetParam(TileParamEnum.SizeX) * tile.GetParam(TileParamEnum.SizeY) * GetWeightFor("bush_cell_size");
            sum += tile.GetParam(TileParamEnum.SizeX) * tile.GetParam(TileParamEnum.SizeY) * GetWeightFor("giant_pinata_cell_size");
            sum += tile.GetParam(TileParamEnum.SizeX) * tile.GetParam(TileParamEnum.SizeY) * GetWeightFor("safe_cell_size");
            sum += tile.GetParam(TileParamEnum.SizeX) * tile.GetParam(TileParamEnum.SizeY) * GetWeightFor("icebar_cell_size");
            sum += tile.GetParam(TileParamEnum.SizeX) * tile.GetParam(TileParamEnum.SizeY) * GetWeightFor("metalbar_cell_size");
            sum += tile.GetParam(TileParamEnum.SizeX) * tile.GetParam(TileParamEnum.SizeY) * GetWeightFor("slotmachine_cell_size");
            sum += tile.GetParam(TileParamEnum.SizeX) * tile.GetParam(TileParamEnum.SizeY) * GetWeightFor("bigmonkey_cell_size");
            sum += tile.IsAnyOf(TileState.AnimalMod) ? GetWeightFor("furry_pal") : 0f;
            sum += tile.IsAnyOf(TileState.SandMod) && tile.Speciality != TileSpeciality.Sand ? GetWeightFor("sand_mod") : 0f;

            FillNeigbours(_tempNeigbourCells, _tempNeighboursSpecs, grid, cell.Coords);
            if (tile.Speciality == TileSpeciality.ColorBomb)
            {
                isBooster = true;

                if (tile.IsAnyOf(TileState.ArmorMod))
                {
                    sum += GetWeightFor("bolt_with_armor");
                }
                else if (!IsExistSwappableCellAround(cell.Coords, grid))
                {
                    sum += GetWeightFor("bolt_blocked_in_corner");
                }
                else if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.ColorBomb))
                {
                    sum += GetWeightFor("bolt_bolt");
                }
                else if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.Bomb)
                         || IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.Propeller)
                    || IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.ColumnBreaker)
                    || IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.RowBreaker))
                {
                    sum += GetWeightFor("bolt_booster");
                }
            }
            else if (IsLineBreaker(tile.Speciality))
            {
                isBooster = true;
                if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.ColorBomb))
                {
                    sum += GetWeightFor("bolt_booster");
                }
                else if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.Bomb))
                {
                    sum += GetWeightFor("bomb_lb");
                }
                else if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.Propeller))
                {
                    sum += GetWeightFor("booster_booster");
                }
                else if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.RowBreaker)
                    || IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.ColumnBreaker))
                {
                    sum += GetWeightFor("booster_booster");
                }
            }
            else if (tile.Speciality == TileSpeciality.Bomb)
            {
                isBooster = true;
                if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.ColorBomb))
                {
                    sum += GetWeightFor("bolt_booster");
                }
                else if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.Bomb))
                {
                    sum += GetWeightFor("booster_booster");
                }
                else if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.Propeller))
                {
                    sum += GetWeightFor("booster_booster");
                }
                else if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.RowBreaker)
                    || IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.ColumnBreaker))
                {
                    sum += GetWeightFor("bomb_lb");
                }
            }
            else if (tile.Speciality == TileSpeciality.Propeller)
            {
                isBooster = true;
                if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.ColorBomb))
                {
                    sum += GetWeightFor("bolt_booster");
                }
                else if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.Bomb))
                {
                    sum += GetWeightFor("booster_booster");
                }
                else if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.Propeller))
                {
                    sum += GetWeightFor("booster_booster");
                }
                else if (IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.RowBreaker)
                         || IsAnyNeigbour(_tempNeighboursSpecs, TileSpeciality.ColumnBreaker))
                {
                    sum += GetWeightFor("booster_booster");
                }
            }

            else if (tile.Speciality == TileSpeciality.DropItem)
            {
                Cell cellBelow = null;
                bool isCollectorBelow = false;

                var hasWall = cell.Walls != null && (cell.Walls.Directions & CardinalDirections.S) == 0;
                var hasInvisibleWall = cell.InvisibleWalls != null && (cell.InvisibleWalls.Directions & CardinalDirections.S) == 0;
                var hasDestructibleWall = cell.DestructibleWalls != null && cell.DestructibleWalls.DestructibleWall
                    [DestructibleWalls.CardinalToIndex(CardinalDirections.S)].Count > 0;
                
                if (hasWall || hasInvisibleWall || hasDestructibleWall)
                {
                    for (int i = cell.Coords.Y - 1; i >= 0; i--)
                    {
                        if (grid.TryGetCell(new Coords(cell.Coords.X, i), out cellBelow))
                        {
                            if (cellBelow.Is(CellState.Despawner))
                            {
                                isCollectorBelow = true;
                                break;
                            }
                            else if (cellBelow.Walls != null && (cellBelow.Walls.Directions & CardinalDirections.N_S) != 0)
                            {
                                break;
                            }
                            else if (cellBelow.InvisibleWalls != null && (cellBelow.InvisibleWalls.Directions & CardinalDirections.N_S) != 0)
                            {
                                break;
                            }
                            else if (cellBelow.DestructibleWalls != null &&
                                     cellBelow.DestructibleWalls.DestructibleWall[DestructibleWalls.CardinalToIndex(CardinalDirections.N)].Count > 0 &&
                                     cellBelow.DestructibleWalls.DestructibleWall[DestructibleWalls.CardinalToIndex(CardinalDirections.S)].Count > 0)
                            {
                                break;
                            }

                        }
                        else
                        {
                            break;
                        }
                    }
                }

                if (!isCollectorBelow)
                {
                    sum += GetWeightFor("drop_item_on_dead_column");
                }
            }

            return sum;
        }

        private static bool IsAnyNeigbour(TileSpeciality[] neighbour, TileSpeciality spec)
        {
            for (int i = 0; i < neighbour.Length; i++)
            {
                if (neighbour[i] == spec) return true;
            }

            return false;
        }

        private static bool IsExistSwappableCellAround(Coords coords, Grid grid)
        {
            if (HasNotNullSwappableTileAtCoords(new Coords(coords.X + 1, coords.Y), grid)) return true;
            if (HasNotNullSwappableTileAtCoords(new Coords(coords.X - 1, coords.Y), grid)) return true;
            if (HasNotNullSwappableTileAtCoords(new Coords(coords.X, coords.Y + 1), grid)) return true;
            if (HasNotNullSwappableTileAtCoords(new Coords(coords.X, coords.Y - 1), grid)) return true;

            return false;
        }

        private static bool HasNotNullSwappableTileAtCoords(Coords coords, Grid grid)
        {
            if (grid.TryGetCell(coords, out var cell))
            {
                if (cell.IsBaseCellSwappable()
                    && !(cell.Tile is null)
                    && !cell.Tile.IsAnyOf(TileState.ArmorMod)
                    && !cell.Tile.IsAnyOf(TileState.NotSwappable))
                {
                    return true;
                }
            }

            return false;
        }

        private static void FillNeigbours(Cell[] arrayCells, TileSpeciality[] arraySpecs, Grid grid, Coords center)
        {
            Cell cell;
            grid.TryGetCell(new Coords(center.X, center.Y + 1), out cell);
            arrayCells[0] = cell;
            arraySpecs[0] = cell != null && !cell.Tile.IsNull() ? cell.Tile.Speciality : TileSpeciality.None;
            grid.TryGetCell(new Coords(center.X + 1, center.Y), out cell);
            arrayCells[1] = cell;
            arraySpecs[1] = cell != null && !cell.Tile.IsNull() ? cell.Tile.Speciality : TileSpeciality.None;
            grid.TryGetCell(new Coords(center.X, center.Y - 1), out cell);
            arrayCells[2] = cell;
            arraySpecs[2] = cell != null && !cell.Tile.IsNull() ? cell.Tile.Speciality : TileSpeciality.None;
            grid.TryGetCell(new Coords(center.X - 1, center.Y), out cell);
            arrayCells[3] = cell;
            arraySpecs[3] = cell != null && !cell.Tile.IsNull() ? cell.Tile.Speciality : TileSpeciality.None;
        }

        private static float GetWeightFor(string id)
        {
            switch (id)
            {
                case "blocked_spawner":
                    return 1800f;
                case "empty_cell":
                    return 2f;
                case "ivy":
                    return 1f;
                case "grass":
                    return 1f;
                case "chain":
                    return 5f;
                case "ice":
                    return 5f;
                case "vase":
                    return 4f;
                case "egg":
                    return 2f;
                case "flowerpot":
                    return 2f;
                case "squid_hp":
                    return 2f;
                case "bowling_hp":
                    return 7f;
                case "bush_hp":
                    return 5f;
                case "safe_hp":
                    return 4f;
                case "squid_cell_size":
                    return 1f;
                case "bowling_cell_size":
                    return 1f;
                case "bush_cell_size":
                    return 1f;
                case "giant_pinata_cell_size":
                    return 1f;
                case "safe_cell_size":
                    return 1f;
                case "icebar_cell_size":
                    return 1f;
                case "metalbar_cell_size":
                    return 1f;
                case "slotmachine_cell_size":
                    return 1f;
                case "bigmonkey_cell_size":
                    return 1f;
                case "adjacent_hp":
                    return 1f;
                case "not_out_of_bees":
                    return 1f;
                case "not_out_of_rabbits":
                    return 1f;
                case "shelf_empty":
                    return 1f;
                case "hen":
                    return 4f;
                case "monkey":
                    return 2f;
                case "furry_pal":
                    return 1f;
                case "bolt_bolt":
                    return -25f;
                case "bomb_lb":
                    return -10f;
                case "bolt_booster":
                    return -5f;
                case "bolt_with_armor":
                    return 6f;
                case "bolt_blocked_in_corner":
                    return 6f;
                case "booster_booster":
                    return -3f;
                case "excess_booster":
                    return 5f;
                case "sand_mod":
                    return 3f;
                case "drop_item_on_dead_column":
                    return 1000f;
                case "TileSpeciality.None":
                    return 0.5f;
                case "TileSpeciality.Sticker":
                    return 1f;
                case "TileSpeciality.BlinkingTile":
                    return 0f;
                case "TileSpeciality.Propeller":
                    return 0.2f;
                case "TileSpeciality.RowBreaker":
                    return 0.2f;
                case "TileSpeciality.ColumnBreaker":
                    return 0.2f;
                case "TileSpeciality.Bomb":
                    return 0.2f;
                case "TileSpeciality.ColorBomb":
                    return 0f;
                case "TileSpeciality.DropItem":
                    return 1f;
                case "TileSpeciality.Litter":
                    return 1f;
                case "TileSpeciality.Pinata":
                    return 1f;
                case "TileSpeciality.Sand":
                    return 3f;
                case "TileSpeciality.Frame":
                    return 1f;
                case "TileSpeciality.ColorCrate":
                    return 1f;
                case "TileSpeciality.Watermelon":
                    return 1f;
                case "TileSpeciality.MoneyBag":
                    return 1f;
                case "TileSpeciality.Penguin":
                    return 1f;
                case "TileSpeciality.Egg":
                    return 1f;
                case "TileSpeciality.Bird":
                    return 0.5f;
                case "TileSpeciality.Sheep":
                    return 1f;
                case "TileSpeciality.Banana":
                    return 0.5f;
                case "TileSpeciality.Monkey":
                    return 1f;
                case "TileSpeciality.BigMonkey":
                    return 1f;
                case "TileSpeciality.Skunk":
                    return 1f;
                case "TileSpeciality.Hen":
                    return 1f;
                case "TileSpeciality.Chicken":
                    return 0.5f;
                case "TileSpeciality.Hive":
                    return 1f;
                case "TileSpeciality.Bee":
                    return -2f;
                case "TileSpeciality.Mole":
                    return 1f;
                case "TileSpeciality.Squid":
                    return 1f;
                case "TileSpeciality.Toad":
                    return 4f;
                case "TileSpeciality.MagicHat":
                    return 1f;
                case "TileSpeciality.Bowling":
                    return 7f;
                case "TileSpeciality.Bush":
                    return 5f;
                case "TileSpeciality.Soda":
                    return 4f;
                case "TileSpeciality.Safe":
                    return 4f;
                case "TileSpeciality.FlowerPot":
                    return 1f;
                case "TileSpeciality.IceBar":
                    return 5f;
                case "TileSpeciality.DynamiteBox":
                    return 8f;
                case "TileSpeciality.GiantPinata":
                    return 3f;
                case "TileSpeciality.MetalBar":
                    return 5f;
                case "TileSpeciality.Shelf":
                    return 1f;
                case "TileSpeciality.JellyFish":
                    return 7f;
                case "TileSpeciality.FireWorks":
                    return 2f;
                case "TileSpeciality.SlotMachine":
                    return 5f;
            }

            UnityEngine.Debug.LogError("Unmatched id: " + id);
            return 0f;
        }

        private static float GetGoalWeight(GoalType goal)
        {
            return 3000f;
        }
    }
}
