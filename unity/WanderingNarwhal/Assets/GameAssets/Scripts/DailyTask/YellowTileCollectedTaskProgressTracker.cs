using BBB;

namespace GameAssets.Scripts.DailyTask
{
    public class YellowTileCollectedTaskProgressTracker : BaseTileCollectedTaskProgressTracker
    {
        public override string TaskType => "YellowTile";

        protected override void TileCollectedEventHandler(TileCollectedEvent ev)
        {
            var tile = ev.Arg0;
            if (tile != null && tile.Speciality != TileSpeciality.ColorCrate && tile.Kind == TileKinds.Yellow)
            {
                AddProgress(1);
            }
        }
    }
}