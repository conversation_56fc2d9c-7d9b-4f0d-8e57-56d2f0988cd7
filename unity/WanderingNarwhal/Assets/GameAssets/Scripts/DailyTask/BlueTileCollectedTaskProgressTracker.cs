using BBB;

namespace GameAssets.Scripts.DailyTask
{
    public class BlueTileCollectedTaskProgressTracker : BaseTileCollectedTaskProgressTracker
    {
        public override string TaskType => "BlueTile";

        protected override void TileCollectedEventHandler(TileCollectedEvent ev)
        {
            var tile = ev.Arg0;
            if (tile != null && tile.Speciality != TileSpeciality.ColorCrate && tile.Kind == TileKinds.Blue)
            {
                AddProgress(1);
            }
        }
    }
}