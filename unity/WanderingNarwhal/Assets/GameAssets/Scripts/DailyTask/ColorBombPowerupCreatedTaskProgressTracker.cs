using BBB;

namespace GameAssets.Scripts.DailyTask
{
    public class ColorBombPowerupCreatedTaskProgressTracker : BasePowerupCreatedTaskProgressTracker
    {
        public override string TaskType => "ColorBomb";

        protected override void PowerupCreatedEventHandler(PowerupCreatedEvent ev)
        {
            var tile = ev.Arg0;
            if (tile.Speciality is TileSpeciality.ColorBomb)
            {
                AddProgress(1);
            }
        }
    }
}