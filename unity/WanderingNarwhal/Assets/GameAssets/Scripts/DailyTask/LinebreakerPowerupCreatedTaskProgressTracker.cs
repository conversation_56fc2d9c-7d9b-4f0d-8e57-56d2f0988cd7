using BBB;

namespace GameAssets.Scripts.DailyTask
{
    public class RocketPowerupCreatedTaskProgressTracker : BasePowerupCreatedTaskProgressTracker
    {
        public override string TaskType => "Rocket";

        protected override void PowerupCreatedEventHandler(PowerupCreatedEvent ev)
        {
            var tile = ev.Arg0;
            if (tile.Speciality is TileSpeciality.ColumnBreaker or TileSpeciality.RowBreaker)
            {
                AddProgress(1);
            }
        }
    }
}