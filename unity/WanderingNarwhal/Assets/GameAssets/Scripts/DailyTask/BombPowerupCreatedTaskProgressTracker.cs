using BBB;

namespace GameAssets.Scripts.DailyTask
{
    public class BombPowerupCreatedTaskProgressTracker : BasePowerupCreatedTaskProgressTracker
    {
        public override string TaskType => "Bomb";

        protected override void PowerupCreatedEventHandler(PowerupCreatedEvent ev)
        {
            var tile = ev.Arg0;
            if (tile.Speciality is TileSpeciality.Bomb)
            {
                AddProgress(1);
            }
        }
    }
}