using BBB;

namespace GameAssets.Scripts.DailyTask
{
    public class PropellerPowerupCreatedTaskProgressTracker : BasePowerupCreatedTaskProgressTracker
    {
        public override string TaskType => "Propeller";

        protected override void PowerupCreatedEventHandler(PowerupCreatedEvent ev)
        {
            var tile = ev.Arg0;
            if (tile.Speciality is TileSpeciality.Propeller)
            {
                AddProgress(1);
            }
        }
    }
}