using BBB;
using BBB.DI;
using UnityEngine;

namespace GameAssets.Scripts.Tutorial.Core
{
    public class Match3TutorialSetup : BaseTutorialStepSetup
    {
        public override void Init(IContext context)
        {
            base.Init(context);

            EnterCondition = () => (LastScreenType & ScreenType.Levels) > 0 && PlayerManager.CurrentLevel.Config.Uid == LevelUid;
            ExitCondition = () => LocationManager.GetLevelStage(LevelUid) > 0;
        }
    }
}