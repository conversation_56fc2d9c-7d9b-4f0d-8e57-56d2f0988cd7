using UnityEngine;
using UnityEngine.Networking;
using System.Text;
using System.Collections;
using System.Collections.Generic;
using System.Security.Cryptography;
using System;
using System.Globalization;
using BBB.MiniJSON;
using BBB;
using BBB.Core;
using BBB.DI;
using BebopBee;
using Bebopbee.Core.Systems.GamemessengerBase;
using Core.Rpc.Commands;
using Core.RPC;
using GameAssets.Scripts.Messages;
using RPC.Command;
using UnityEngine.Profiling;

//list of requests each one with different name
using RequestBatch = System.Collections.Generic.List<RPCRequest>;
//list of RequestBatch
using RequestBatchList = System.Collections.Generic.List<System.Collections.Generic.List<RPCRequest>>;
//dict <RequestBatch, parameters for each>
using BatchesParameterDict =
    System.Collections.Generic.Dictionary<System.Collections.Generic.List<RPCRequest>, System.Collections.Generic.Dictionary<string, object>>;

public class RPCService : BbbMonoBehaviour, IRPCService, IGameMessageListener, IContextInitializable
{
    public const string PreSalt = "9oZrwkCg4XT1";
    public const string PostSalt = "KXHkfsyru0Ph";
    private const string RpcServiceName = "rpc";
    private const string RpcName = "rpc";

    private static string _serverUrl;
    public event Action<string> OnConnectionErrorDetails;

    private readonly RequestBatchList _requestBatches = new ();

    private IDictionary<string, string> _payload;
    private bool _noConnectionStopSending;
    private string _sessionId;

    private const int Timeout = GameSettings.LongRequestTimeout;
    private string _advertisingId;
    private IEventDispatcher _dispatcher;

    public void InitializeByContext(IContext context)
    {
        _dispatcher = context.Resolve<IEventDispatcher>();
        Application.RequestAdvertisingIdentifierAsync((advertisingId, _, _) => { _advertisingId = advertisingId; });
    }

    public static void SetServerUrl(string url)
    {
        _serverUrl = url;
        BDebug.LogFormat(LogCat.Rpc, "RPC url = {0}", url);
    }

    public void SetSession(string sessionId)
    {
        _sessionId = sessionId;
    }

    public void AddRequest(RPCRequest request)
    {
        var reqName = request.Name;
        BDebug.LogFormat(LogCat.Rpc, "Adding request {0}", reqName);
        var addNewBatch = true;
        var requestBatchesEmpty = _requestBatches.Count == 0;

        if (!requestBatchesEmpty)
        {
            var lastBatchHasOneOfThese = false;
            var lastBatch = _requestBatches[^1];
            RPCRequest prevReq = null;

            foreach (var batchReq in lastBatch)
            {
                if (!string.Equals(batchReq.Name, reqName)) continue;
                
                lastBatchHasOneOfThese = true;
                prevReq = batchReq;
                break;
            }

            if (lastBatchHasOneOfThese && request.Override)
            {
                lastBatch.Remove(prevReq);
                lastBatchHasOneOfThese = false;
            }

            if (!lastBatchHasOneOfThese)
            {
                lastBatch.Add(request);
                addNewBatch = false;
            }
        }

        //add new batch if batchlist is empty, or if the last one has a request with the same name
        if (!addNewBatch)
            return;

        var newBatch = new List<RPCRequest> {request};
        _requestBatches.Add(newBatch);
    }

    public void ExecuteRequests(Action<Dictionary<string, RPCResponse>> callback = null)
    {
#if BBB_DEBUG && BBB_LOG
        BDebug.Log(LogCat.Rpc, "Executing requests:");
        var i = 0;
        foreach (var batch in _requestBatches)
        {
            BDebug.LogFormat(LogCat.Rpc, "Batch {0}:", i);
            foreach (var request in batch)
            {
                BDebug.Log(LogCat.Rpc, request.Name);
            }
            i++;
        }
#endif
        if (string.IsNullOrEmpty(_serverUrl))
        {
            throw new Exception("Server URL Is Missing");
        }

        if (!_noConnectionStopSending)
        {
            DoAllRequests(callback);
        }
    }

    private void DoAllRequests(Action<Dictionary<string, RPCResponse>> callback)
    {
        var parameters = CreateParameters();
        if (parameters.Count > 0)
        {
            if (Application.isEditor && !Application.isPlaying)
            {
                var e = _DoRequests(callback, parameters);
                while (e.MoveNext())
                {
                }
            }
            else
            {
                StartCoroutine(_DoRequests(callback, parameters));
            }
        }
        else
        {
            callback.SafeInvoke(null);
        }
    }

    private BatchesParameterDict CreateParameters()
    {
        var batchParams = new BatchesParameterDict();

        foreach (var batch in _requestBatches)
        {
            var parameters = new Dictionary<string, object>();

            foreach (var req in batch)
            {
                if (req.Name == null)
                {
                    BDebug.LogErrorFormat(LogCat.Rpc, "<color=blue>Wrong Request Name: {0} params: {1}", req.Name, req.Params);
                }
                else
                {
                    parameters.Add(req.Name, req.Params);
                }
            }

            batchParams.Add(batch, parameters);
        }

        _requestBatches.Clear();

        return batchParams;
    }

    private WWWForm GenerateWwForm(IDictionary<string, object> parameters)
    {
        Profiler.BeginSample("RPCService.GenerateWwForm");
        IDictionary<string, object> postdata = new Dictionary<string, object>();
        postdata[RpcName] = parameters;
        Profiler.BeginSample("RPCService.GeneratePayload");
        postdata["payload"] = GeneratePayload();
        Profiler.EndSample();

        Profiler.BeginSample("RPCService Json.Serialize");
        var jsonStr = Json.Serialize(postdata);
        Profiler.EndSample();

        if (AppDefinesConverter.BbbDebug)
        {
            BDebug.Log(LogCat.Rpc, jsonStr);
        }

        Profiler.BeginSample("RPCService.GenerateWwForm - Calculate hash");
        SHA1 sha = new SHA1CryptoServiceProvider();

        var planeBytes = Encoding.UTF8.GetBytes(PreSalt + jsonStr + PostSalt);
        var hashBytes = sha.ComputeHash(planeBytes);
        var builder = new StringBuilder();

        // Loop through each byte of the hashed data  
        // and format each one as a hexadecimal string.
        Array.ForEach(hashBytes, _ => builder.Append(_.ToString("x2")));

        // Return the hexadecimal string. 
        var sha1 = builder.ToString();
        Profiler.EndSample();

        var form = new WWWForm();
        form.AddField("data", jsonStr);
        form.AddField("cksum", sha1);
        return form;
    }

    //the only difference between sync and not sinc is the line yield www/while(!www.isDone), refactor properly
    //when there's time
    private IEnumerator _DoRequests(Action<Dictionary<string, RPCResponse>> callback, BatchesParameterDict parameters)
    {
        var finishedWwWs = new List<KeyValuePair<RequestBatch, string>>();
        var responsesDict = new Dictionary<string, RPCResponse>();
        var allEndedOk = true;

        var networkErrors = 0;
        var urlBuilder = new StringBuilder();
        foreach (var item in parameters)
        {
            var form = GenerateWwForm(item.Value);
            var url = urlBuilder.Clear().Append(_serverUrl).Append(RpcServiceName).ToString();
            var www = UnityWebRequest.Post(url, form);
            www.timeout = Timeout;
            
            BDebug.Log(LogCat.Rpc, $"Sending Request: {url}");
            yield return www.SendWebRequest();

#if UNITY_EDITOR
            while (!www.isDone)
            {
                yield return true;
            }
#endif
            
            var networkError = www.isNetworkError;
            var shouldBreak = false;

            try
            {
                switch (networkError)
                {
                    case false when ProcessRpcResponse(www.downloadHandler.text, out var rpcResponse):
                    {
                        var kv = new KeyValuePair<RequestBatch, string>(item.Key, rpcResponse);
                        finishedWwWs.Add(kv);
                        break;
                    }
                    case false:
                        shouldBreak = true;
                        break;
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                networkError = true;
            }

            BDebug.Log(LogCat.Rpc, $"Request response error: {networkError}");

            if (networkError)
            {
                networkErrors++;
            }

            if (shouldBreak)
            {
                allEndedOk = false;
                www.Dispose();
                break;
            }

            _requestBatches.Remove(item.Key);
            www.Dispose();
        }

        var networkStatusOk = networkErrors == 0;
        BDebug.Log(LogCat.Rpc, $"Request networkStatusOk: {networkStatusOk}");
        if (allEndedOk)
        {
            _noConnectionStopSending = false;

            foreach (var kv in finishedWwWs)
            {
                ProcessResponse(kv.Value, kv.Key, responsesDict);
            }

            if (callback == null) yield break;
            BDebug.Log(LogCat.Rpc, "Calling done callback");
            callback(responsesDict);
        }
        else
        {
            HandleConnectionError(() =>
            {
                _noConnectionStopSending = false;
                StartCoroutine(_DoRequests(callback, parameters));
            });

            yield return null;
        }
    }

    private static string GetResponseError(object response)
    {
        string result = null;

        if (response is Dictionary<string, object> responseDict && responseDict.TryGetValue("error", out var errorObj))
        {
            result = errorObj as string;
        }

        return result;
    }

    private static bool ProcessRpcResponse(string data, out string response)
    {
        Dictionary<string, object> rpcResponse;
        try
        {
            rpcResponse = Json.Deserialize(data) as Dictionary<string, object>;
        }
        catch (Exception e)
        {
            BDebug.LogError(LogCat.Rpc, "Failed to deserialize response!");
            BDebug.LogError(LogCat.Rpc, e.Message);
            BDebug.LogError(LogCat.Rpc, data);
            throw;
        }

        if (rpcResponse == null)
        {
            BDebug.LogError(LogCat.Rpc, "Failed to deserialize response!");
            BDebug.LogError(LogCat.Rpc, data);
        }

        SHA1 sha1 = new SHA1CryptoServiceProvider();
        
        response = null;
        
        if (rpcResponse == null) return false;
        
        bool valid;
        if (rpcResponse["data"].ToString().Equals(ResultType.NoInternet.ToString()))
        {
            response = (string) rpcResponse["data"];
            valid = true;
        }
        else
        {
            var planeBytes = Encoding.UTF8.GetBytes(PreSalt + rpcResponse["data"] + PostSalt);
            var hash = sha1.ComputeHash(planeBytes);
            var delimitedHexHash = BitConverter.ToString(hash);
            var hexHash = delimitedHexHash.Replace("-", "").ToLowerInvariant();
            valid = hexHash.Equals(((string) rpcResponse["cksum"]).ToLowerInvariant());
            if (valid)
            {
                response = (string) rpcResponse["data"];
#if !UNITY_CLOUD_BUILD && !UNITY_JENKINS_BUILD
                if (AppDefinesConverter.BbbDebug)
                {
                    BDebug.Log(LogCat.Rpc, $"response data is: {response}");
                }
#endif
            }
            else
            {
                BDebug.LogErrorFormat(LogCat.Rpc, "Missmaching checksum: {0} server:{1}", hexHash, rpcResponse["cksum"]);
            }
        }

        return valid;
    }

    private void ProcessResponse(string responseText, RequestBatch batch, Dictionary<string, RPCResponse> responsesDict)
    {
        if (responseText.Equals(ResultType.NoInternet.ToString()))
        {
            foreach (var request in batch)
            {
                request.IsCompleted = true;
                var constructedResponse = new RPCResponse {Success = true, Result = "", ErrorMessage = string.Empty, Status = ResultType.NoInternet};
                if (!responsesDict.TryAdd(request.Name, constructedResponse))
                {
                    BDebug.LogError(LogCat.Rpc, $"Trying to add a duplicate: {request.Name} response key: {responseText}");
                }

                request.Callback?.Invoke(constructedResponse);
            }
        }
        else
        {
            var response = Json.Deserialize(responseText) as Dictionary<string, object> ?? new Dictionary<string, object>();
            foreach (var pair in response)
            {
                if (GetResponseError(pair.Value) == RPCResponse.ERROR_SESSIONENDED)
                {
                    HandleSessionEndedError(() => _noConnectionStopSending = false);
                    BDebug.Log(LogCat.Rpc, $"HandleSessionEndedError {pair.Key}");
                    continue;
                }

                if (GetResponseError(pair.Value) == RPCResponse.ERROR_FORCESYNC)
                {
                    HandleForceSyncError();
                    BDebug.Log(LogCat.Rpc, $"HandleForceSyncError {pair.Key}");
                    continue;
                }

                RPCRequest currentReq = null;

                foreach (var req in batch)
                {
                    if (req.Name != pair.Key) continue;
                    
                    currentReq = req;
                    break;
                }

                if (currentReq == null)
                {
                    continue;
                }

                currentReq.IsCompleted = true;

                try
                {
                    var constructedResponse = new RPCResponse {Success = true, Result = pair.Value, ErrorMessage = string.Empty, Status = ResultType.Success};
                    if (!responsesDict.TryAdd(pair.Key, constructedResponse))
                    {
                        BDebug.LogError(LogCat.Rpc, $"Trying to add a duplicate {pair.Key} response key: {responseText}");
                    }


                    currentReq.Callback?.Invoke(constructedResponse);
                }
                catch (Exception exception)
                {
                    BDebug.LogFormat(LogCat.Rpc, "Response: {0}", responseText);
                    Debug.LogException(exception);
                }
            }
        }
    }

    private IDictionary<string, string> GeneratePayload()
    {
        _payload ??= new Dictionary<string, string>
        {
            { "dmodel", SystemInfo.deviceModel },
            { "dname", SystemInfo.deviceName },
            { "sysmem", SystemInfo.systemMemorySize.ToString() },
            { "gdevice", SystemInfo.graphicsDeviceID.ToString() },
            { "gname", SystemInfo.graphicsDeviceName },
            { "gvendor", SystemInfo.graphicsDeviceVendor },
            { "gvendorid", SystemInfo.graphicsDeviceVendorID.ToString() },
            { "gdeviceversion", SystemInfo.graphicsDeviceVersion },
            { "gmem", SystemInfo.graphicsMemorySize.ToString() },
            { "os", SystemInfo.operatingSystem },
            { "cpu", SystemInfo.processorCount.ToString() },
            { "cputype", SystemInfo.processorType },
            { "platform", PlatformUtil.GetPlatform() },
            { "tz", Util.GetTimeZone() },
            { "BundleIdentifier", PlatformUtil.GetAppIdentifier() },
            { "BundleVersion", PlatformUtil.GetAppVersion() },
            { "BundleDisplayName", PlatformUtil.GetBundleDisplayName() },
            { "BuildNumber", CurrentBundleVersion.GetBuildCode() },
            { "language", LanguageHelper.GetISOCodeFromSystemLanguage() },
            { "country", PlatformUtil.GetCurrentCountryCode() }
        };

        _payload["uid"] = MultiDevice.GetUserId();
        _payload["device_uid"] = MultiDevice.GetDeviceId();
        _payload["advertising_id"] = _advertisingId;
        _payload["session_id"] = _sessionId.IsNullOrEmpty() ? string.Empty : _sessionId;
        _payload["t"] = Util.UnixLocalTimestamp().ToString(CultureInfo.InvariantCulture);
        _payload["tzoffset"] = Util.GetTimeZoneOffset().ToString();
        if (AppDefinesConverter.BbbDebug)
        {
            _payload["debug_mode"] = "true";
        }
            
        return _payload;
    }

    private void HandleConnectionError(Action callback)
    {
        _noConnectionStopSending = true;

        BDebug.LogError(LogCat.Rpc, "Connection lost, HandleConnectionError called!");
    }

    private void HandleSessionEndedError(Action callback)
    {
        _noConnectionStopSending = true;

        Time.timeScale = 0;
        var serverSyncRequiredEvent = _dispatcher.GetMessage<ServerSyncRequiredEvent>();
        serverSyncRequiredEvent.Set(callback);
        _dispatcher.TriggerEvent(serverSyncRequiredEvent);

        BDebug.LogError(LogCat.Rpc, "Connection lost, HandleSessionEndedError called!");
    }

    private void HandleForceSyncError()
    {
        _dispatcher.TriggerEvent(_dispatcher.GetMessage<ForceSyncEvent>());

        BDebug.LogError(LogCat.Rpc, "Connection lost, HandleSessionEndedError called!");
    }

    public void OnMessage(IGameMessage message)
    {
        if (message is RpcServiceMessage result)
        {
            result.Apply(this);
        }
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        OnConnectionErrorDetails = null;
    }
}

public class ForceSyncEvent : IEvent
{
    
}
public class SessionRenewEvent : IEvent
{
    
}

public class ServerSyncRequiredEvent : Message<Action>
{
}